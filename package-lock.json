{"name": "yupao-frontend", "version": "0.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "yupao-frontend", "version": "0.0.0", "dependencies": {"axios": "^0.27.2", "qs": "^6.10.5", "vant": "^3.4.8", "vue": "^3.2.25", "vue-router": "4"}, "devDependencies": {"@types/node": "^18.11.0", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^2.3.1", "typescript": "^4.5.4", "vite": "^2.9.5", "vite-plugin-style-import": "^1.4.1", "vue-tsc": "^0.34.7"}}, "node_modules/@babel/parser": {"version": "7.17.9", "resolved": "https://registry.npmmirror.com/@babel/parser/-/parser-7.17.9.tgz", "integrity": "sha512-vqUSBLP8dQHFPdPi9bc5GK9vRkYHJ49fsZdtoJ8EQ8ibpwk5rPKfvNIwChB0KVXcIjcepEBBd2VHC5r9Gy8ueg==", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@popperjs/core": {"version": "2.11.5", "resolved": "https://registry.npmmirror.com/@popperjs/core/-/core-2.11.5.tgz", "integrity": "sha512-9X2obfABZuDVLCgPK9aX0a/x4jaOEweTTWE2+9sr0Qqqevj2Uv5XorvusThmc9XGYpS9yI+fhh8RTafBtGposw=="}, "node_modules/@rollup/pluginutils": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz", "integrity": "sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==", "dev": true, "dependencies": {"estree-walker": "^2.0.1", "picomatch": "^2.2.2"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/@types/node": {"version": "18.11.0", "resolved": "https://registry.npmmirror.com/@types/node/-/node-18.11.0.tgz", "integrity": "sha512-IOXCvVRToe7e0ny7HpT/X9Rb2RYtElG1a+VshjwT00HxrM2dWBApHQoqsI6WiY7Q03vdf2bCrIGzVrkF/5t10w==", "dev": true}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "https://registry.npmmirror.com/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==", "dev": true, "license": "MIT"}, "node_modules/@vant/icons": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/@vant/icons/-/icons-1.8.0.tgz", "integrity": "sha512-sKfEUo2/CkQFuERxvkuF6mGQZDKu3IQdj5rV9Fm0weJXtchDSSQ+zt8qPCNUEhh9Y8shy5PzxbvAfOOkCwlCXg=="}, "node_modules/@vant/popperjs": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@vant/popperjs/-/popperjs-1.1.0.tgz", "integrity": "sha512-8MD1gz146awV/uPxYjz4pet22f7a9YVKqk7T+gFkWFwT9mEcrIUEg/xPrdOnWKLP9puXyYtm7oVfSDSefZ/p/w==", "dependencies": {"@popperjs/core": "^2.9.2"}}, "node_modules/@vant/use": {"version": "1.3.6", "resolved": "https://registry.npmmirror.com/@vant/use/-/use-1.3.6.tgz", "integrity": "sha512-3z+nywPaV2F5BdJO7RQxWlgfzJeEOmViD2yHMb7Tg+R4NR/7iQskqW8v2Cnv9FWSJgTOSHlcr7UzeLpiTAP4HA=="}, "node_modules/@vitejs/plugin-vue": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-2.3.1.tgz", "integrity": "sha512-YNzBt8+jt6bSwpt7LP890U1UcTOIZZxfpE5WOJ638PNxSEKOqAi0+FSKS0nVeukfdZ0Ai/H7AFd6k3hayfGZqQ==", "dev": true, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"vite": "^2.5.10", "vue": "^3.2.25"}}, "node_modules/@volar/code-gen": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/code-gen/-/code-gen-0.34.10.tgz", "integrity": "sha512-Pygl26uA4CuQcDgNndeTSNOYF+NbShcV+rwWRy/nRNv1JB++1EbaQ60/ti8c5zTRoL4a8OtipKMq9Sw8LzpRIw==", "dev": true, "dependencies": {"@volar/source-map": "0.34.10"}}, "node_modules/@volar/source-map": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/source-map/-/source-map-0.34.10.tgz", "integrity": "sha512-DBSUGNJB2B08U6Ut14ZJSEOcBS7eV/aiinhoLbMrEe/HJtZRcnPuyE8f0c2BvmRM2LK8WQx77V54/lw/Ra8WDA==", "dev": true}, "node_modules/@volar/vue-code-gen": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/vue-code-gen/-/vue-code-gen-0.34.10.tgz", "integrity": "sha512-oK5gat5AHllSMJzY+UMbttJvAjoUGzicXxLHoIwb6DTHpfcf2pADYUndiw5kSYHo+2Xd/+U1c9D8FUOJ+JHAFw==", "dev": true, "dependencies": {"@volar/code-gen": "0.34.10", "@volar/source-map": "0.34.10", "@vue/compiler-core": "^3.2.31", "@vue/compiler-dom": "^3.2.31", "@vue/shared": "^3.2.31"}}, "node_modules/@volar/vue-typescript": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/vue-typescript/-/vue-typescript-0.34.10.tgz", "integrity": "sha512-FCGSqLC+T/AcBUFXoFniPKLa/fLslBuHsepUmId8dG5ROXZhQaJ5h4fkA87247SWb7z4o9mI6v86xevXEjRVKw==", "dev": true, "dependencies": {"@volar/code-gen": "0.34.10", "@volar/source-map": "0.34.10", "@volar/vue-code-gen": "0.34.10", "@vue/compiler-sfc": "^3.2.31", "@vue/reactivity": "^3.2.31"}}, "node_modules/@vue/compiler-core": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.2.33.tgz", "integrity": "sha512-AAmr52ji3Zhk7IKIuigX2osWWsb2nQE5xsdFYjdnmtQ4gymmqXbjLvkSE174+fF3A3kstYrTgGkqgOEbsdLDpw==", "dependencies": {"@babel/parser": "^7.16.4", "@vue/shared": "3.2.33", "estree-walker": "^2.0.2", "source-map": "^0.6.1"}}, "node_modules/@vue/compiler-dom": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.2.33.tgz", "integrity": "sha512-GhiG1C8X98Xz9QUX/RlA6/kgPBWJkjq0Rq6//5XTAGSYrTMBgcLpP9+CnlUg1TFxnnCVughAG+KZl28XJqw8uQ==", "dependencies": {"@vue/compiler-core": "3.2.33", "@vue/shared": "3.2.33"}}, "node_modules/@vue/compiler-sfc": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.2.33.tgz", "integrity": "sha512-H8D0WqagCr295pQjUYyO8P3IejM3vEzeCO1apzByAEaAR/WimhMYczHfZVvlCE/9yBaEu/eu9RdiWr0kF8b71Q==", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.33", "@vue/compiler-dom": "3.2.33", "@vue/compiler-ssr": "3.2.33", "@vue/reactivity-transform": "3.2.33", "@vue/shared": "3.2.33", "estree-walker": "^2.0.2", "magic-string": "^0.25.7", "postcss": "^8.1.10", "source-map": "^0.6.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.2.33.tgz", "integrity": "sha512-XQh1Xdk3VquDpXsnoCd7JnMoWec9CfAzQDQsaMcSU79OrrO2PNR0ErlIjm/mGq3GmBfkQjzZACV+7GhfRB8xMQ==", "dependencies": {"@vue/compiler-dom": "3.2.33", "@vue/shared": "3.2.33"}}, "node_modules/@vue/devtools-api": {"version": "6.1.4", "resolved": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.4.tgz", "integrity": "sha512-IiA0SvDrJEgXvVxjNkHPFfDx6SXw0b/TUkqMcDZWNg9fnCAHbTpoo59YfJ9QLFkwa3raau5vSlRVzMSLDnfdtQ=="}, "node_modules/@vue/reactivity": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.2.33.tgz", "integrity": "sha512-62Sq0mp9/0bLmDuxuLD5CIaMG2susFAGARLuZ/5jkU1FCf9EDbwUuF+BO8Ub3Rbodx0ziIecM/NsmyjardBxfQ==", "dependencies": {"@vue/shared": "3.2.33"}}, "node_modules/@vue/reactivity-transform": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.2.33.tgz", "integrity": "sha512-4UL5KOIvSQb254aqenW4q34qMXbfZcmEsV/yVidLUgvwYQQ/D21bGX3DlgPUGI3c4C+iOnNmDCkIxkILoX/Pyw==", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.33", "@vue/shared": "3.2.33", "estree-walker": "^2.0.2", "magic-string": "^0.25.7"}}, "node_modules/@vue/runtime-core": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.2.33.tgz", "integrity": "sha512-N2D2vfaXsBPhzCV3JsXQa2NECjxP3eXgZlFqKh4tgakp3iX6LCGv76DLlc+IfFZq+TW10Y8QUfeihXOupJ1dGw==", "dependencies": {"@vue/reactivity": "3.2.33", "@vue/shared": "3.2.33"}}, "node_modules/@vue/runtime-dom": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.2.33.tgz", "integrity": "sha512-LSrJ6W7CZTSUygX5s8aFkraDWlO6K4geOwA3quFF2O+hC3QuAMZt/0Xb7JKE3C4JD4pFwCSO7oCrZmZ0BIJUnw==", "dependencies": {"@vue/runtime-core": "3.2.33", "@vue/shared": "3.2.33", "csstype": "^2.6.8"}}, "node_modules/@vue/server-renderer": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.2.33.tgz", "integrity": "sha512-4jpJHRD4ORv8PlbYi+/MfP8ec1okz6rybe36MdpkDrGIdEItHEUyaHSKvz+ptNEyQpALmmVfRteHkU9F8vxOew==", "dependencies": {"@vue/compiler-ssr": "3.2.33", "@vue/shared": "3.2.33"}, "peerDependencies": {"vue": "3.2.33"}}, "node_modules/@vue/shared": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/shared/-/shared-3.2.33.tgz", "integrity": "sha512-UBc1Pg1T3yZ97vsA2ueER0F6GbJebLHYlEi4ou1H5YL4KWvMOOWwpYo9/QpWq93wxKG6Wo13IY74Hcn/f7c7Bg=="}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/axios": {"version": "0.27.2", "resolved": "https://registry.npmmirror.com/axios/-/axios-0.27.2.tgz", "integrity": "sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==", "dependencies": {"follow-redirects": "^1.14.9", "form-data": "^4.0.0"}}, "node_modules/call-bind": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "node_modules/camel-case": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz", "integrity": "sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==", "dev": true, "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/capital-case": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/capital-case/-/capital-case-1.0.4.tgz", "integrity": "sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "node_modules/change-case": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/change-case/-/change-case-4.1.2.tgz", "integrity": "sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==", "dev": true, "dependencies": {"camel-case": "^4.1.2", "capital-case": "^1.0.4", "constant-case": "^3.0.4", "dot-case": "^3.0.4", "header-case": "^2.0.4", "no-case": "^3.0.4", "param-case": "^3.0.4", "pascal-case": "^3.1.2", "path-case": "^3.0.4", "sentence-case": "^3.0.4", "snake-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/constant-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/constant-case/-/constant-case-3.0.4.tgz", "integrity": "sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case": "^2.0.2"}}, "node_modules/csstype": {"version": "2.6.20", "resolved": "https://registry.npmmirror.com/csstype/-/csstype-2.6.20.tgz", "integrity": "sha512-/WwNkdXfckNgw6S5R125rrW8ez139lBHWouiBvX8dfMFtcn6V81REDqnH7+CRpRipfYlyU1CmOnOxrmGcFOjeA=="}, "node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/dot-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz", "integrity": "sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/es-module-lexer": {"version": "0.9.3", "resolved": "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "integrity": "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==", "dev": true}, "node_modules/esbuild": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild/-/esbuild-0.14.38.tgz", "integrity": "sha512-12fzJ0fsm7gVZX1YQ1InkOE5f9Tl7cgf6JPYXRJtPIoE0zkWAbHdPHVPPaLi9tYAcEBqheGzqLn/3RdTOyBfcA==", "dev": true, "hasInstallScript": true, "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"esbuild-android-64": "0.14.38", "esbuild-android-arm64": "0.14.38", "esbuild-darwin-64": "0.14.38", "esbuild-darwin-arm64": "0.14.38", "esbuild-freebsd-64": "0.14.38", "esbuild-freebsd-arm64": "0.14.38", "esbuild-linux-32": "0.14.38", "esbuild-linux-64": "0.14.38", "esbuild-linux-arm": "0.14.38", "esbuild-linux-arm64": "0.14.38", "esbuild-linux-mips64le": "0.14.38", "esbuild-linux-ppc64le": "0.14.38", "esbuild-linux-riscv64": "0.14.38", "esbuild-linux-s390x": "0.14.38", "esbuild-netbsd-64": "0.14.38", "esbuild-openbsd-64": "0.14.38", "esbuild-sunos-64": "0.14.38", "esbuild-windows-32": "0.14.38", "esbuild-windows-64": "0.14.38", "esbuild-windows-arm64": "0.14.38"}}, "node_modules/esbuild-android-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-android-64/-/esbuild-android-64-0.14.38.tgz", "integrity": "sha512-aRFxR3scRKkbmNuGAK+Gee3+yFxkTJO/cx83Dkyzo4CnQl/2zVSurtG6+G86EQIZ+w+VYngVyK7P3HyTBKu3nw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/esbuild-android-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-android-arm64/-/esbuild-android-arm64-0.14.38.tgz", "integrity": "sha512-L2NgQRWuHFI89IIZIlpAcINy9FvBk6xFVZ7xGdOwIm8VyhX1vNCEqUJO3DPSSy945Gzdg98cxtNt8Grv1CsyhA==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/esbuild-darwin-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-darwin-64/-/esbuild-darwin-64-0.14.38.tgz", "integrity": "sha512-5JJvgXkX87Pd1Og0u/NJuO7TSqAikAcQQ74gyJ87bqWRVeouky84ICoV4sN6VV53aTW+NE87qLdGY4QA2S7KNA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/esbuild-darwin-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.14.38.tgz", "integrity": "sha512-eqF+OejMI3mC5Dlo9Kdq/Ilbki9sQBw3QlHW3wjLmsLh+quNfHmGMp3Ly1eWm981iGBMdbtSS9+LRvR2T8B3eQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/esbuild-freebsd-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.14.38.tgz", "integrity": "sha512-epnPbhZUt93xV5cgeY36ZxPXDsQeO55DppzsIgWM8vgiG/Rz+qYDLmh5ts3e+Ln1wA9dQ+nZmVHw+RjaW3I5Ig==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/esbuild-freebsd-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.14.38.tgz", "integrity": "sha512-/9icXUYJWherhk+y5fjPI5yNUdFPtXHQlwP7/K/zg8t8lQdHVj20SqU9/udQmeUo5pDFHMYzcEFfJqgOVeKNNQ==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-32": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-32/-/esbuild-linux-32-0.14.38.tgz", "integrity": "sha512-QfgfeNHRFvr2XeHFzP8kOZVnal3QvST3A0cgq32ZrHjSMFTdgXhMhmWdKzRXP/PKcfv3e2OW9tT9PpcjNvaq6g==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-64/-/esbuild-linux-64-0.14.38.tgz", "integrity": "sha512-uuZHNmqcs+Bj1qiW9k/HZU3FtIHmYiuxZ/6Aa+/KHb/pFKr7R3aVqvxlAudYI9Fw3St0VCPfv7QBpUITSmBR1Q==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-arm": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-arm/-/esbuild-linux-arm-0.14.38.tgz", "integrity": "sha512-FiFvQe8J3VKTDXG01JbvoVRXQ0x6UZwyrU4IaLBZeq39Bsbatd94Fuc3F1RGqPF5RbIWW7RvkVQjn79ejzysnA==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.14.38.tgz", "integrity": "sha512-HlMGZTEsBrXrivr64eZ/EO0NQM8H8DuSENRok9d+Jtvq8hOLzrxfsAT9U94K3KOGk2XgCmkaI2KD8hX7F97lvA==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-mips64le": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.14.38.tgz", "integrity": "sha512-qd1dLf2v7QBiI5wwfil9j0HG/5YMFBAmMVmdeokbNAMbcg49p25t6IlJFXAeLzogv1AvgaXRXvgFNhScYEUXGQ==", "cpu": ["mips64el"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-ppc64le": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.14.38.tgz", "integrity": "sha512-mnbEm7o69gTl60jSuK+nn+pRsRHGtDPfzhrqEUXyCl7CTOCLtWN2bhK8bgsdp6J/2NyS/wHBjs1x8aBWwP2X9Q==", "cpu": ["ppc64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-riscv64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.14.38.tgz", "integrity": "sha512-+p6YKYbuV72uikChRk14FSyNJZ4WfYkffj6Af0/Tw63/6TJX6TnIKE+6D3xtEc7DeDth1fjUOEqm+ApKFXbbVQ==", "cpu": ["riscv64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-linux-s390x": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.14.38.tgz", "integrity": "sha512-0zUsiDkGJiMHxBQ7JDU8jbaanUY975CdOW1YDrurjrM0vWHfjv9tLQsW9GSyEb/heSK1L5gaweRjzfUVBFoybQ==", "cpu": ["s390x"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/esbuild-netbsd-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.14.38.tgz", "integrity": "sha512-cljBAApVwkpnJZfnRVThpRBGzCi+a+V9Ofb1fVkKhtrPLDYlHLrSYGtmnoTVWDQdU516qYI8+wOgcGZ4XIZh0Q==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/esbuild-openbsd-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.14.38.tgz", "integrity": "sha512-CDswYr2PWPGEPpLDUO50mL3WO/07EMjnZDNKpmaxUPsrW+kVM3LoAqr/CE8UbzugpEiflYqJsGPLirThRB18IQ==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/esbuild-sunos-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-sunos-64/-/esbuild-sunos-64-0.14.38.tgz", "integrity": "sha512-2mfIoYW58gKcC3bck0j7lD3RZkqYA7MmujFYmSn9l6TiIcAMpuEvqksO+ntBgbLep/eyjpgdplF7b+4T9VJGOA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/esbuild-windows-32": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-windows-32/-/esbuild-windows-32-0.14.38.tgz", "integrity": "sha512-L2BmEeFZATAvU+FJzJiRLFUP+d9RHN+QXpgaOrs2klshoAm1AE6Us4X6fS9k33Uy5SzScn2TpcgecbqJza1Hjw==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/esbuild-windows-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-windows-64/-/esbuild-windows-64-0.14.38.tgz", "integrity": "sha512-Khy4wVmebnzue8aeSXLC+6clo/hRYeNIm0DyikoEqX+3w3rcvrhzpoix0S+MF9vzh6JFskkIGD7Zx47ODJNyCw==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/esbuild-windows-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.14.38.tgz", "integrity": "sha512-k3FGCNmHBkqdJXuJszdWciAH77PukEyDsdIryEHn9cKLQFxzhT39dSumeTuggaQcXY57UlmLGIkklWZo2qzHpw==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="}, "node_modules/follow-redirects": {"version": "1.15.1", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.1.tgz", "integrity": "sha512-yLAMQs+k0b2m7cVxpS1VKJVvoz7SS9Td1zss3XRwXj+ZDH00RJgnuLx7E44wx02kQLrdM3aOOy+FpzS7+8OizA==", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fs-extra": {"version": "10.1.0", "resolved": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "dev": true, "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "node_modules/get-intrinsic": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha512-Jfm3OyCxHh9DJyc28qGk+JmfkpO41A4XkneDSujN9MDXrm4oDKdHvndhZ2dN94+ERNfkYJWDclW6k2L/ZGHjXA==", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "node_modules/graceful-fs": {"version": "4.2.10", "resolved": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.10.tgz", "integrity": "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==", "dev": true}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "engines": {"node": ">= 0.4"}}, "node_modules/header-case": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/header-case/-/header-case-2.0.4.tgz", "integrity": "sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==", "dev": true, "dependencies": {"capital-case": "^1.0.4", "tslib": "^2.0.3"}}, "node_modules/is-core-module": {"version": "2.9.0", "resolved": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.9.0.tgz", "integrity": "sha512-+5FPy5PnwmO3lvfMb0AsoPaBG+5KHUI0wYFXOtYPnVVVspTFUuMZNfNaNVRt3FZadstu2c8x23vykRW/NBoU6A==", "dev": true, "dependencies": {"has": "^1.0.3"}}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "dev": true, "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/lower-case": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz", "integrity": "sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/magic-string": {"version": "0.25.9", "resolved": "https://registry.npmmirror.com/magic-string/-/magic-string-0.25.9.tgz", "integrity": "sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==", "dependencies": {"sourcemap-codec": "^1.4.8"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "node_modules/nanoid": {"version": "3.3.3", "resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.3.tgz", "integrity": "sha512-p1sjXuopFs0xg+fPASzQ28agW1oHD7xDsd9Xkf3T15H3c/cifrFHVwrh74PdoklAPi+i7MdRsE47vm2r6JoB+w==", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/no-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz", "integrity": "sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==", "dev": true, "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/object-inspect": {"version": "1.12.2", "resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ=="}, "node_modules/param-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz", "integrity": "sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==", "dev": true, "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/pascal-case": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz", "integrity": "sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/path-case/-/path-case-3.0.4.tgz", "integrity": "sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==", "dev": true, "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true}, "node_modules/picocolors": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "engines": {"node": ">=8.6"}}, "node_modules/postcss": {"version": "8.4.12", "resolved": "https://registry.npmmirror.com/postcss/-/postcss-8.4.12.tgz", "integrity": "sha512-lg6eITwYe9v6Hr5CncVbK70SoioNQIq81nsaG86ev5hAidQvmOeETBqs7jm43K2F5/Ley3ytDtriImV6TpNiSg==", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/qs": {"version": "6.10.5", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.10.5.tgz", "integrity": "sha512-O5RlPh0VFtR78y79rgcgKK4wbAI0C5zGVLztOIdpWX6ep368q5Hv6XRxDvXuZ9q3C6v+e3n8UfZZJw7IIG27eQ==", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}}, "node_modules/resolve": {"version": "1.22.0", "resolved": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "integrity": "sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==", "dev": true, "dependencies": {"is-core-module": "^2.8.1", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}}, "node_modules/rollup": {"version": "2.70.2", "resolved": "https://registry.npmmirror.com/rollup/-/rollup-2.70.2.tgz", "integrity": "sha512-EitogNZnfku65I1DD5Mxe8JYRUCy0hkK5X84IlDtUs+O6JRMpRciXTzyCUuX11b5L5pvjH+OmFXiQ3XjabcXgg==", "dev": true, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=10.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/sentence-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/sentence-case/-/sentence-case-3.0.4.tgz", "integrity": "sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==", "dev": true, "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "node_modules/side-channel": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "node_modules/snake-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/snake-case/-/snake-case-3.0.4.tgz", "integrity": "sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==", "dev": true, "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz", "integrity": "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==", "engines": {"node": ">=0.10.0"}}, "node_modules/sourcemap-codec": {"version": "1.4.8", "resolved": "https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "integrity": "sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA=="}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/tslib": {"version": "2.4.0", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "dev": true}, "node_modules/typescript": {"version": "4.6.3", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-4.6.3.tgz", "integrity": "sha512-yNIatDa5iaofVozS/uQJEl3JRWLKKGJKh6Yaiv0GLGSuhpFJe7P3SbHZ8/yjAHRQwKRoA6YZqlfjXWmVzoVSMw==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/universalify": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz", "integrity": "sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==", "dev": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/upper-case": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/upper-case/-/upper-case-2.0.2.tgz", "integrity": "sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/upper-case-first": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/upper-case-first/-/upper-case-first-2.0.2.tgz", "integrity": "sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==", "dev": true, "dependencies": {"tslib": "^2.0.3"}}, "node_modules/vant": {"version": "3.4.8", "resolved": "https://registry.npmmirror.com/vant/-/vant-3.4.8.tgz", "integrity": "sha512-xPAiPdCiaZbcdbVplVh4N2lmYzDAr2UdadkUHoa9uwfHS9/dab4f8TigfCFzlfAEk53U1J6w+6qZ+whtt4T3hw==", "dependencies": {"@vant/icons": "^1.8.0", "@vant/popperjs": "^1.1.0", "@vant/use": "^1.3.6"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/vite": {"version": "2.9.5", "resolved": "https://registry.npmmirror.com/vite/-/vite-2.9.5.tgz", "integrity": "sha512-dvMN64X2YEQgSXF1lYabKXw3BbN6e+BL67+P3Vy4MacnY+UzT1AfkHiioFSi9+uiDUiaDy7Ax/LQqivk6orilg==", "dev": true, "dependencies": {"esbuild": "^0.14.27", "postcss": "^8.4.12", "resolve": "^1.22.0", "rollup": "^2.59.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": ">=12.2.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"less": "*", "sass": "*", "stylus": "*"}, "peerDependenciesMeta": {"less": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}}}, "node_modules/vite-plugin-style-import": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/vite-plugin-style-import/-/vite-plugin-style-import-1.4.1.tgz", "integrity": "sha512-lJCRvm7+So0hHdnSJiJPg9gD5mxtL6YY0jmhEph+k7ArpsyvqOh6han2kG5htbWWDZxHkUN9d1BuTFL//yCLLQ==", "dev": true, "dependencies": {"@rollup/pluginutils": "^4.1.2", "change-case": "^4.1.2", "debug": "^4.3.3", "es-module-lexer": "^0.9.3", "fs-extra": "^10.0.0", "magic-string": "^0.25.7"}, "peerDependencies": {"vite": ">=2.0.0"}}, "node_modules/vue": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/vue/-/vue-3.2.33.tgz", "integrity": "sha512-si1ExAlDUrLSIg/V7D/GgA4twJwfsfgG+t9w10z38HhL/HA07132pUQ2KuwAo8qbCyMJ9e6OqrmWrOCr+jW7ZQ==", "dependencies": {"@vue/compiler-dom": "3.2.33", "@vue/compiler-sfc": "3.2.33", "@vue/runtime-dom": "3.2.33", "@vue/server-renderer": "3.2.33", "@vue/shared": "3.2.33"}}, "node_modules/vue-router": {"version": "4.0.16", "resolved": "https://registry.npmmirror.com/vue-router/-/vue-router-4.0.16.tgz", "integrity": "sha512-Jc<PERSON>7cb8QJLBWE+DfxGUL3xUDOae/8nhM1KVdnudadTAORbuxIC/xAydC5Zr/VLHUDQi1ppuTF5/rjBGzgzrJNA==", "dependencies": {"@vue/devtools-api": "^6.0.0"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vue-tsc": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/vue-tsc/-/vue-tsc-0.34.10.tgz", "integrity": "sha512-rWU4SjDqk9ylQN2hbnyP+rEu8W2a712DWUmciX6rDnId1m8sN/cuypTKjWjHHjaBLWNKULoEakRTOvrQ4ainhw==", "dev": true, "dependencies": {"@volar/vue-typescript": "0.34.10"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "peerDependencies": {"typescript": "*"}}}, "dependencies": {"@babel/parser": {"version": "7.17.9", "resolved": "https://registry.npmmirror.com/@babel/parser/-/parser-7.17.9.tgz", "integrity": "sha512-vqUSBLP8dQHFPdPi9bc5GK9vRkYHJ49fsZdtoJ8EQ8ibpwk5rPKfvNIwChB0KVXcIjcepEBBd2VHC5r9Gy8ueg=="}, "@popperjs/core": {"version": "2.11.5", "resolved": "https://registry.npmmirror.com/@popperjs/core/-/core-2.11.5.tgz", "integrity": "sha512-9X2obfABZuDVLCgPK9aX0a/x4jaOEweTTWE2+9sr0Qqqevj2Uv5XorvusThmc9XGYpS9yI+fhh8RTafBtGposw=="}, "@rollup/pluginutils": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz", "integrity": "sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==", "dev": true, "requires": {"estree-walker": "^2.0.1", "picomatch": "^2.2.2"}}, "@types/node": {"version": "18.11.0", "resolved": "https://registry.npmmirror.com/@types/node/-/node-18.11.0.tgz", "integrity": "sha512-IOXCvVRToe7e0ny7HpT/X9Rb2RYtElG1a+VshjwT00HxrM2dWBApHQoqsI6WiY7Q03vdf2bCrIGzVrkF/5t10w==", "dev": true}, "@types/qs": {"version": "6.14.0", "resolved": "https://registry.npmmirror.com/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==", "dev": true}, "@vant/icons": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/@vant/icons/-/icons-1.8.0.tgz", "integrity": "sha512-sKfEUo2/CkQFuERxvkuF6mGQZDKu3IQdj5rV9Fm0weJXtchDSSQ+zt8qPCNUEhh9Y8shy5PzxbvAfOOkCwlCXg=="}, "@vant/popperjs": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@vant/popperjs/-/popperjs-1.1.0.tgz", "integrity": "sha512-8MD1gz146awV/uPxYjz4pet22f7a9YVKqk7T+gFkWFwT9mEcrIUEg/xPrdOnWKLP9puXyYtm7oVfSDSefZ/p/w==", "requires": {"@popperjs/core": "^2.9.2"}}, "@vant/use": {"version": "1.3.6", "resolved": "https://registry.npmmirror.com/@vant/use/-/use-1.3.6.tgz", "integrity": "sha512-3z+nywPaV2F5BdJO7RQxWlgfzJeEOmViD2yHMb7Tg+R4NR/7iQskqW8v2Cnv9FWSJgTOSHlcr7UzeLpiTAP4HA=="}, "@vitejs/plugin-vue": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-2.3.1.tgz", "integrity": "sha512-YNzBt8+jt6bSwpt7LP890U1UcTOIZZxfpE5WOJ638PNxSEKOqAi0+FSKS0nVeukfdZ0Ai/H7AFd6k3hayfGZqQ==", "dev": true, "requires": {}}, "@volar/code-gen": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/code-gen/-/code-gen-0.34.10.tgz", "integrity": "sha512-Pygl26uA4CuQcDgNndeTSNOYF+NbShcV+rwWRy/nRNv1JB++1EbaQ60/ti8c5zTRoL4a8OtipKMq9Sw8LzpRIw==", "dev": true, "requires": {"@volar/source-map": "0.34.10"}}, "@volar/source-map": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/source-map/-/source-map-0.34.10.tgz", "integrity": "sha512-DBSUGNJB2B08U6Ut14ZJSEOcBS7eV/aiinhoLbMrEe/HJtZRcnPuyE8f0c2BvmRM2LK8WQx77V54/lw/Ra8WDA==", "dev": true}, "@volar/vue-code-gen": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/vue-code-gen/-/vue-code-gen-0.34.10.tgz", "integrity": "sha512-oK5gat5AHllSMJzY+UMbttJvAjoUGzicXxLHoIwb6DTHpfcf2pADYUndiw5kSYHo+2Xd/+U1c9D8FUOJ+JHAFw==", "dev": true, "requires": {"@volar/code-gen": "0.34.10", "@volar/source-map": "0.34.10", "@vue/compiler-core": "^3.2.31", "@vue/compiler-dom": "^3.2.31", "@vue/shared": "^3.2.31"}}, "@volar/vue-typescript": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/@volar/vue-typescript/-/vue-typescript-0.34.10.tgz", "integrity": "sha512-FCGSqLC+T/AcBUFXoFniPKLa/fLslBuHsepUmId8dG5ROXZhQaJ5h4fkA87247SWb7z4o9mI6v86xevXEjRVKw==", "dev": true, "requires": {"@volar/code-gen": "0.34.10", "@volar/source-map": "0.34.10", "@volar/vue-code-gen": "0.34.10", "@vue/compiler-sfc": "^3.2.31", "@vue/reactivity": "^3.2.31"}}, "@vue/compiler-core": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.2.33.tgz", "integrity": "sha512-AAmr52ji3Zhk7IKIuigX2osWWsb2nQE5xsdFYjdnmtQ4gymmqXbjLvkSE174+fF3A3kstYrTgGkqgOEbsdLDpw==", "requires": {"@babel/parser": "^7.16.4", "@vue/shared": "3.2.33", "estree-walker": "^2.0.2", "source-map": "^0.6.1"}}, "@vue/compiler-dom": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.2.33.tgz", "integrity": "sha512-GhiG1C8X98Xz9QUX/RlA6/kgPBWJkjq0Rq6//5XTAGSYrTMBgcLpP9+CnlUg1TFxnnCVughAG+KZl28XJqw8uQ==", "requires": {"@vue/compiler-core": "3.2.33", "@vue/shared": "3.2.33"}}, "@vue/compiler-sfc": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.2.33.tgz", "integrity": "sha512-H8D0WqagCr295pQjUYyO8P3IejM3vEzeCO1apzByAEaAR/WimhMYczHfZVvlCE/9yBaEu/eu9RdiWr0kF8b71Q==", "requires": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.33", "@vue/compiler-dom": "3.2.33", "@vue/compiler-ssr": "3.2.33", "@vue/reactivity-transform": "3.2.33", "@vue/shared": "3.2.33", "estree-walker": "^2.0.2", "magic-string": "^0.25.7", "postcss": "^8.1.10", "source-map": "^0.6.1"}}, "@vue/compiler-ssr": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.2.33.tgz", "integrity": "sha512-XQh1Xdk3VquDpXsnoCd7JnMoWec9CfAzQDQsaMcSU79OrrO2PNR0ErlIjm/mGq3GmBfkQjzZACV+7GhfRB8xMQ==", "requires": {"@vue/compiler-dom": "3.2.33", "@vue/shared": "3.2.33"}}, "@vue/devtools-api": {"version": "6.1.4", "resolved": "https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.1.4.tgz", "integrity": "sha512-IiA0SvDrJEgXvVxjNkHPFfDx6SXw0b/TUkqMcDZWNg9fnCAHbTpoo59YfJ9QLFkwa3raau5vSlRVzMSLDnfdtQ=="}, "@vue/reactivity": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.2.33.tgz", "integrity": "sha512-62Sq0mp9/0bLmDuxuLD5CIaMG2susFAGARLuZ/5jkU1FCf9EDbwUuF+BO8Ub3Rbodx0ziIecM/NsmyjardBxfQ==", "requires": {"@vue/shared": "3.2.33"}}, "@vue/reactivity-transform": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.2.33.tgz", "integrity": "sha512-4UL5KOIvSQb254aqenW4q34qMXbfZcmEsV/yVidLUgvwYQQ/D21bGX3DlgPUGI3c4C+iOnNmDCkIxkILoX/Pyw==", "requires": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.33", "@vue/shared": "3.2.33", "estree-walker": "^2.0.2", "magic-string": "^0.25.7"}}, "@vue/runtime-core": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.2.33.tgz", "integrity": "sha512-N2D2vfaXsBPhzCV3JsXQa2NECjxP3eXgZlFqKh4tgakp3iX6LCGv76DLlc+IfFZq+TW10Y8QUfeihXOupJ1dGw==", "requires": {"@vue/reactivity": "3.2.33", "@vue/shared": "3.2.33"}}, "@vue/runtime-dom": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.2.33.tgz", "integrity": "sha512-LSrJ6W7CZTSUygX5s8aFkraDWlO6K4geOwA3quFF2O+hC3QuAMZt/0Xb7JKE3C4JD4pFwCSO7oCrZmZ0BIJUnw==", "requires": {"@vue/runtime-core": "3.2.33", "@vue/shared": "3.2.33", "csstype": "^2.6.8"}}, "@vue/server-renderer": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.2.33.tgz", "integrity": "sha512-4jpJHRD4ORv8PlbYi+/MfP8ec1okz6rybe36MdpkDrGIdEItHEUyaHSKvz+ptNEyQpALmmVfRteHkU9F8vxOew==", "requires": {"@vue/compiler-ssr": "3.2.33", "@vue/shared": "3.2.33"}}, "@vue/shared": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/@vue/shared/-/shared-3.2.33.tgz", "integrity": "sha512-UBc1Pg1T3yZ97vsA2ueER0F6GbJebLHYlEi4ou1H5YL4KWvMOOWwpYo9/QpWq93wxKG6Wo13IY74Hcn/f7c7Bg=="}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "axios": {"version": "0.27.2", "resolved": "https://registry.npmmirror.com/axios/-/axios-0.27.2.tgz", "integrity": "sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==", "requires": {"follow-redirects": "^1.14.9", "form-data": "^4.0.0"}}, "call-bind": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "camel-case": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz", "integrity": "sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==", "dev": true, "requires": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "capital-case": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/capital-case/-/capital-case-1.0.4.tgz", "integrity": "sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "change-case": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/change-case/-/change-case-4.1.2.tgz", "integrity": "sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==", "dev": true, "requires": {"camel-case": "^4.1.2", "capital-case": "^1.0.4", "constant-case": "^3.0.4", "dot-case": "^3.0.4", "header-case": "^2.0.4", "no-case": "^3.0.4", "param-case": "^3.0.4", "pascal-case": "^3.1.2", "path-case": "^3.0.4", "sentence-case": "^3.0.4", "snake-case": "^3.0.4", "tslib": "^2.0.3"}}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "constant-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/constant-case/-/constant-case-3.0.4.tgz", "integrity": "sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case": "^2.0.2"}}, "csstype": {"version": "2.6.20", "resolved": "https://registry.npmmirror.com/csstype/-/csstype-2.6.20.tgz", "integrity": "sha512-/WwNkdXfckNgw6S5R125rrW8ez139lBHWouiBvX8dfMFtcn6V81REDqnH7+CRpRipfYlyU1CmOnOxrmGcFOjeA=="}, "debug": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "requires": {"ms": "2.1.2"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "dot-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz", "integrity": "sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "es-module-lexer": {"version": "0.9.3", "resolved": "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "integrity": "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==", "dev": true}, "esbuild": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild/-/esbuild-0.14.38.tgz", "integrity": "sha512-12fzJ0fsm7gVZX1YQ1InkOE5f9Tl7cgf6JPYXRJtPIoE0zkWAbHdPHVPPaLi9tYAcEBqheGzqLn/3RdTOyBfcA==", "dev": true, "requires": {"esbuild-android-64": "0.14.38", "esbuild-android-arm64": "0.14.38", "esbuild-darwin-64": "0.14.38", "esbuild-darwin-arm64": "0.14.38", "esbuild-freebsd-64": "0.14.38", "esbuild-freebsd-arm64": "0.14.38", "esbuild-linux-32": "0.14.38", "esbuild-linux-64": "0.14.38", "esbuild-linux-arm": "0.14.38", "esbuild-linux-arm64": "0.14.38", "esbuild-linux-mips64le": "0.14.38", "esbuild-linux-ppc64le": "0.14.38", "esbuild-linux-riscv64": "0.14.38", "esbuild-linux-s390x": "0.14.38", "esbuild-netbsd-64": "0.14.38", "esbuild-openbsd-64": "0.14.38", "esbuild-sunos-64": "0.14.38", "esbuild-windows-32": "0.14.38", "esbuild-windows-64": "0.14.38", "esbuild-windows-arm64": "0.14.38"}}, "esbuild-android-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-android-64/-/esbuild-android-64-0.14.38.tgz", "integrity": "sha512-aRFxR3scRKkbmNuGAK+Gee3+yFxkTJO/cx83Dkyzo4CnQl/2zVSurtG6+G86EQIZ+w+VYngVyK7P3HyTBKu3nw==", "dev": true, "optional": true}, "esbuild-android-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-android-arm64/-/esbuild-android-arm64-0.14.38.tgz", "integrity": "sha512-L2NgQRWuHFI89IIZIlpAcINy9FvBk6xFVZ7xGdOwIm8VyhX1vNCEqUJO3DPSSy945Gzdg98cxtNt8Grv1CsyhA==", "dev": true, "optional": true}, "esbuild-darwin-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-darwin-64/-/esbuild-darwin-64-0.14.38.tgz", "integrity": "sha512-5JJvgXkX87Pd1Og0u/NJuO7TSqAikAcQQ74gyJ87bqWRVeouky84ICoV4sN6VV53aTW+NE87qLdGY4QA2S7KNA==", "dev": true, "optional": true}, "esbuild-darwin-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.14.38.tgz", "integrity": "sha512-eqF+OejMI3mC5Dlo9Kdq/Ilbki9sQBw3QlHW3wjLmsLh+quNfHmGMp3Ly1eWm981iGBMdbtSS9+LRvR2T8B3eQ==", "dev": true, "optional": true}, "esbuild-freebsd-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.14.38.tgz", "integrity": "sha512-epnPbhZUt93xV5cgeY36ZxPXDsQeO55DppzsIgWM8vgiG/Rz+qYDLmh5ts3e+Ln1wA9dQ+nZmVHw+RjaW3I5Ig==", "dev": true, "optional": true}, "esbuild-freebsd-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.14.38.tgz", "integrity": "sha512-/9icXUYJWherhk+y5fjPI5yNUdFPtXHQlwP7/K/zg8t8lQdHVj20SqU9/udQmeUo5pDFHMYzcEFfJqgOVeKNNQ==", "dev": true, "optional": true}, "esbuild-linux-32": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-32/-/esbuild-linux-32-0.14.38.tgz", "integrity": "sha512-QfgfeNHRFvr2XeHFzP8kOZVnal3QvST3A0cgq32ZrHjSMFTdgXhMhmWdKzRXP/PKcfv3e2OW9tT9PpcjNvaq6g==", "dev": true, "optional": true}, "esbuild-linux-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-64/-/esbuild-linux-64-0.14.38.tgz", "integrity": "sha512-uuZHNmqcs+Bj1qiW9k/HZU3FtIHmYiuxZ/6Aa+/KHb/pFKr7R3aVqvxlAudYI9Fw3St0VCPfv7QBpUITSmBR1Q==", "dev": true, "optional": true}, "esbuild-linux-arm": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-arm/-/esbuild-linux-arm-0.14.38.tgz", "integrity": "sha512-FiFvQe8J3VKTDXG01JbvoVRXQ0x6UZwyrU4IaLBZeq39Bsbatd94Fuc3F1RGqPF5RbIWW7RvkVQjn79ejzysnA==", "dev": true, "optional": true}, "esbuild-linux-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.14.38.tgz", "integrity": "sha512-HlMGZTEsBrXrivr64eZ/EO0NQM8H8DuSENRok9d+Jtvq8hOLzrxfsAT9U94K3KOGk2XgCmkaI2KD8hX7F97lvA==", "dev": true, "optional": true}, "esbuild-linux-mips64le": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.14.38.tgz", "integrity": "sha512-qd1dLf2v7QBiI5wwfil9j0HG/5YMFBAmMVmdeokbNAMbcg49p25t6IlJFXAeLzogv1AvgaXRXvgFNhScYEUXGQ==", "dev": true, "optional": true}, "esbuild-linux-ppc64le": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.14.38.tgz", "integrity": "sha512-mnbEm7o69gTl60jSuK+nn+pRsRHGtDPfzhrqEUXyCl7CTOCLtWN2bhK8bgsdp6J/2NyS/wHBjs1x8aBWwP2X9Q==", "dev": true, "optional": true}, "esbuild-linux-riscv64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.14.38.tgz", "integrity": "sha512-+p6YKYbuV72uikChRk14FSyNJZ4WfYkffj6Af0/Tw63/6TJX6TnIKE+6D3xtEc7DeDth1fjUOEqm+ApKFXbbVQ==", "dev": true, "optional": true}, "esbuild-linux-s390x": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.14.38.tgz", "integrity": "sha512-0zUsiDkGJiMHxBQ7JDU8jbaanUY975CdOW1YDrurjrM0vWHfjv9tLQsW9GSyEb/heSK1L5gaweRjzfUVBFoybQ==", "dev": true, "optional": true}, "esbuild-netbsd-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.14.38.tgz", "integrity": "sha512-cljBAApVwkpnJZfnRVThpRBGzCi+a+V9Ofb1fVkKhtrPLDYlHLrSYGtmnoTVWDQdU516qYI8+wOgcGZ4XIZh0Q==", "dev": true, "optional": true}, "esbuild-openbsd-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.14.38.tgz", "integrity": "sha512-CDswYr2PWPGEPpLDUO50mL3WO/07EMjnZDNKpmaxUPsrW+kVM3LoAqr/CE8UbzugpEiflYqJsGPLirThRB18IQ==", "dev": true, "optional": true}, "esbuild-sunos-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-sunos-64/-/esbuild-sunos-64-0.14.38.tgz", "integrity": "sha512-2mfIoYW58gKcC3bck0j7lD3RZkqYA7MmujFYmSn9l6TiIcAMpuEvqksO+ntBgbLep/eyjpgdplF7b+4T9VJGOA==", "dev": true, "optional": true}, "esbuild-windows-32": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-windows-32/-/esbuild-windows-32-0.14.38.tgz", "integrity": "sha512-L2BmEeFZATAvU+FJzJiRLFUP+d9RHN+QXpgaOrs2klshoAm1AE6Us4X6fS9k33Uy5SzScn2TpcgecbqJza1Hjw==", "dev": true, "optional": true}, "esbuild-windows-64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-windows-64/-/esbuild-windows-64-0.14.38.tgz", "integrity": "sha512-Khy4wVmebnzue8aeSXLC+6clo/hRYeNIm0DyikoEqX+3w3rcvrhzpoix0S+MF9vzh6JFskkIGD7Zx47ODJNyCw==", "dev": true, "optional": true}, "esbuild-windows-arm64": {"version": "0.14.38", "resolved": "https://registry.npmmirror.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.14.38.tgz", "integrity": "sha512-k3FGCNmHBkqdJXuJszdWciAH77PukEyDsdIryEHn9cKLQFxzhT39dSumeTuggaQcXY57UlmLGIkklWZo2qzHpw==", "dev": true, "optional": true}, "estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="}, "follow-redirects": {"version": "1.15.1", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.1.tgz", "integrity": "sha512-yLAMQs+k0b2m7cVxpS1VKJVvoz7SS9Td1zss3XRwXj+ZDH00RJgnuLx7E44wx02kQLrdM3aOOy+FpzS7+8OizA=="}, "form-data": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "fs-extra": {"version": "10.1.0", "resolved": "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz", "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "fsevents": {"version": "2.3.2", "resolved": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "dev": true, "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "get-intrinsic": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha512-Jfm3OyCxHh9DJyc28qGk+JmfkpO41A4XkneDSujN9MDXrm4oDKdHvndhZ2dN94+ERNfkYJWDclW6k2L/ZGHjXA==", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "graceful-fs": {"version": "4.2.10", "resolved": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.10.tgz", "integrity": "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==", "dev": true}, "has": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="}, "header-case": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/header-case/-/header-case-2.0.4.tgz", "integrity": "sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==", "dev": true, "requires": {"capital-case": "^1.0.4", "tslib": "^2.0.3"}}, "is-core-module": {"version": "2.9.0", "resolved": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.9.0.tgz", "integrity": "sha512-+5FPy5PnwmO3lvfMb0AsoPaBG+5KHUI0wYFXOtYPnVVVspTFUuMZNfNaNVRt3FZadstu2c8x23vykRW/NBoU6A==", "dev": true, "requires": {"has": "^1.0.3"}}, "jsonfile": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "lower-case": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz", "integrity": "sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==", "dev": true, "requires": {"tslib": "^2.0.3"}}, "magic-string": {"version": "0.25.9", "resolved": "https://registry.npmmirror.com/magic-string/-/magic-string-0.25.9.tgz", "integrity": "sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==", "requires": {"sourcemap-codec": "^1.4.8"}}, "mime-db": {"version": "1.52.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "requires": {"mime-db": "1.52.0"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "nanoid": {"version": "3.3.3", "resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.3.tgz", "integrity": "sha512-p1sjXuopFs0xg+fPASzQ28agW1oHD7xDsd9Xkf3T15H3c/cifrFHVwrh74PdoklAPi+i7MdRsE47vm2r6JoB+w=="}, "no-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz", "integrity": "sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==", "dev": true, "requires": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ=="}, "param-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz", "integrity": "sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==", "dev": true, "requires": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "pascal-case": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz", "integrity": "sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "path-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/path-case/-/path-case-3.0.4.tgz", "integrity": "sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==", "dev": true, "requires": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="}, "picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true}, "postcss": {"version": "8.4.12", "resolved": "https://registry.npmmirror.com/postcss/-/postcss-8.4.12.tgz", "integrity": "sha512-lg6eITwYe9v6Hr5CncVbK70SoioNQIq81nsaG86ev5hAidQvmOeETBqs7jm43K2F5/Ley3ytDtriImV6TpNiSg==", "requires": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}}, "qs": {"version": "6.10.5", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.10.5.tgz", "integrity": "sha512-O5RlPh0VFtR78y79rgcgKK4wbAI0C5zGVLztOIdpWX6ep368q5Hv6XRxDvXuZ9q3C6v+e3n8UfZZJw7IIG27eQ==", "requires": {"side-channel": "^1.0.4"}}, "resolve": {"version": "1.22.0", "resolved": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "integrity": "sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==", "dev": true, "requires": {"is-core-module": "^2.8.1", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "rollup": {"version": "2.70.2", "resolved": "https://registry.npmmirror.com/rollup/-/rollup-2.70.2.tgz", "integrity": "sha512-EitogNZnfku65I1DD5Mxe8JYRUCy0hkK5X84IlDtUs+O6JRMpRciXTzyCUuX11b5L5pvjH+OmFXiQ3XjabcXgg==", "dev": true, "requires": {"fsevents": "~2.3.2"}}, "sentence-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/sentence-case/-/sentence-case-3.0.4.tgz", "integrity": "sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "side-channel": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "snake-case": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/snake-case/-/snake-case-3.0.4.tgz", "integrity": "sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==", "dev": true, "requires": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}, "source-map-js": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz", "integrity": "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="}, "sourcemap-codec": {"version": "1.4.8", "resolved": "https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "integrity": "sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA=="}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true}, "tslib": {"version": "2.4.0", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "dev": true}, "typescript": {"version": "4.6.3", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-4.6.3.tgz", "integrity": "sha512-yNIatDa5iaofVozS/uQJEl3JRWLKKGJKh6Yaiv0GLGSuhpFJe7P3SbHZ8/yjAHRQwKRoA6YZqlfjXWmVzoVSMw==", "dev": true}, "universalify": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz", "integrity": "sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==", "dev": true}, "upper-case": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/upper-case/-/upper-case-2.0.2.tgz", "integrity": "sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==", "dev": true, "requires": {"tslib": "^2.0.3"}}, "upper-case-first": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/upper-case-first/-/upper-case-first-2.0.2.tgz", "integrity": "sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==", "dev": true, "requires": {"tslib": "^2.0.3"}}, "vant": {"version": "3.4.8", "resolved": "https://registry.npmmirror.com/vant/-/vant-3.4.8.tgz", "integrity": "sha512-xPAiPdCiaZbcdbVplVh4N2lmYzDAr2UdadkUHoa9uwfHS9/dab4f8TigfCFzlfAEk53U1J6w+6qZ+whtt4T3hw==", "requires": {"@vant/icons": "^1.8.0", "@vant/popperjs": "^1.1.0", "@vant/use": "^1.3.6"}}, "vite": {"version": "2.9.5", "resolved": "https://registry.npmmirror.com/vite/-/vite-2.9.5.tgz", "integrity": "sha512-dvMN64X2YEQgSXF1lYabKXw3BbN6e+BL67+P3Vy4MacnY+UzT1AfkHiioFSi9+uiDUiaDy7Ax/LQqivk6orilg==", "dev": true, "requires": {"esbuild": "^0.14.27", "fsevents": "~2.3.2", "postcss": "^8.4.12", "resolve": "^1.22.0", "rollup": "^2.59.0"}}, "vite-plugin-style-import": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/vite-plugin-style-import/-/vite-plugin-style-import-1.4.1.tgz", "integrity": "sha512-lJCRvm7+So0hHdnSJiJPg9gD5mxtL6YY0jmhEph+k7ArpsyvqOh6han2kG5htbWWDZxHkUN9d1BuTFL//yCLLQ==", "dev": true, "requires": {"@rollup/pluginutils": "^4.1.2", "change-case": "^4.1.2", "debug": "^4.3.3", "es-module-lexer": "^0.9.3", "fs-extra": "^10.0.0", "magic-string": "^0.25.7"}}, "vue": {"version": "3.2.33", "resolved": "https://registry.npmmirror.com/vue/-/vue-3.2.33.tgz", "integrity": "sha512-si1ExAlDUrLSIg/V7D/GgA4twJwfsfgG+t9w10z38HhL/HA07132pUQ2KuwAo8qbCyMJ9e6OqrmWrOCr+jW7ZQ==", "requires": {"@vue/compiler-dom": "3.2.33", "@vue/compiler-sfc": "3.2.33", "@vue/runtime-dom": "3.2.33", "@vue/server-renderer": "3.2.33", "@vue/shared": "3.2.33"}}, "vue-router": {"version": "4.0.16", "resolved": "https://registry.npmmirror.com/vue-router/-/vue-router-4.0.16.tgz", "integrity": "sha512-Jc<PERSON>7cb8QJLBWE+DfxGUL3xUDOae/8nhM1KVdnudadTAORbuxIC/xAydC5Zr/VLHUDQi1ppuTF5/rjBGzgzrJNA==", "requires": {"@vue/devtools-api": "^6.0.0"}}, "vue-tsc": {"version": "0.34.10", "resolved": "https://registry.npmmirror.com/vue-tsc/-/vue-tsc-0.34.10.tgz", "integrity": "sha512-rWU4SjDqk9ylQN2hbnyP+rEu8W2a712DWUmciX6rDnId1m8sN/cuypTKjWjHHjaBLWNKULoEakRTOvrQ4ainhw==", "dev": true, "requires": {"@volar/vue-typescript": "0.34.10"}}}}