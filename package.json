{"name": "yupao-frontend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^0.27.2", "qs": "^6.10.5", "vant": "^3.4.8", "vue": "^3.2.25", "vue-router": "4"}, "devDependencies": {"@types/node": "^18.11.0", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^2.3.1", "typescript": "^4.5.4", "vite": "^2.9.5", "vite-plugin-style-import": "^1.4.1", "vue-tsc": "^0.34.7"}}