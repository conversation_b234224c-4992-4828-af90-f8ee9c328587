import {UserType} from "./user";

/**
 * 队伍类别
 */
export type TeamType = {
    id: number;
    name: string;
    description: string;
    expireTime?: Date;
    maxNum: number;
    password?: string,
    // todo 定义枚举值类型，更规范
    status: number;
    createTime: Date;
    updateTime: Date;
    createUser?: UserType;
    hasJoinNum?: number;
    hasJoinUsersid?: number[]; // 已加入用户的ID列表
    userId?: number; // 队伍创建者ID
    hasJoin?: boolean; // 当前用户是否已加入
};
