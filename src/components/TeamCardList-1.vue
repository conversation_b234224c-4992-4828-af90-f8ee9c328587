<template>
  <div class="team-card-list">
    <div
        v-for="team in teamList"
        :key="team.id"
        class="team-card"
        @click="goToTeamDetail(team.id)"
    >
      <!-- 卡片头部 -->
      <div class="card-header">
        <div class="team-avatar">
          <img :src="teamAvatar" alt="队伍头像" class="avatar-image" />
          <div class="member-count">
            {{ team.hasJoinNum }}/{{ team.maxNum }}
          </div>
        </div>
        <div class="team-info">
          <div class="title-row">
            <h3 class="team-name">{{ team.name }}</h3>
            <van-tag
                v-if="team.userId === currentUser?.id"
                type="primary"
                size="small"
                class="creator-badge"
            >
              <van-icon name="crown-o" />
              创建者
            </van-tag>
          </div>
          <p class="team-description">{{ team.description || '暂无描述' }}</p>
          <div class="team-status">
            <van-tag
                :type="team.status === 0 ? 'success' : 'warning'"
                size="small"
                class="status-tag"
            >
              {{ teamStatusEnum[team.status] }}
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 卡片信息 -->
      <div class="card-info">
        <div class="info-item">
          <van-icon name="calendar-o" class="info-icon" />
          <span>{{ formatDate(team.createTime) }}</span>
        </div>
        <div class="info-item" v-if="team.expireTime">
          <van-icon name="clock-o" class="info-icon" />
          <span>{{ formatDate(team.expireTime) }}</span>
        </div>
      </div>

      <!-- 卡片底部操作 -->
      <div class="card-footer" @click.stop>
        <van-button
            type="primary"
            size="small"
            plain
            round
            class="detail-btn"
            @click="goToTeamDetail(team.id)"
        >
          <van-icon name="info-o" />
          查看详情
        </van-button>

        <!-- 快速加入按钮 -->
        <van-button
            v-if="team.userId !== currentUser?.id && !team.hasJoin"
            type="success"
            size="small"
            round
            class="quick-join-btn"
            @click="preJoinTeam(team)"
        >
          <van-icon name="plus" />
          快速加入
        </van-button>

        <!-- 队长快速操作 -->
        <van-button
            v-if="team.userId === currentUser?.id"
            type="warning"
            size="small"
            plain
            round
            class="quick-edit-btn"
            @click="doUpdateTeam(team.id)"
        >
          <van-icon name="edit" />
          编辑
        </van-button>

        <!-- 已加入标识 -->
        <div
            v-if="team.userId !== currentUser?.id && team.hasJoin"
            class="joined-indicator"
        >
          <van-icon name="success" />
          <span>已加入</span>
        </div>
      </div>
    </div>

    <!-- 密码对话框 -->
    <van-dialog
        v-model:show="showPasswordDialog"
        title="请输入队伍密码"
        :show-cancel-button="true"
        confirm-button-text="加入"
        cancel-button-text="取消"
        class="password-dialog"
        @confirm="doJoinTeam"
        @cancel="doJoinCancel"
    >
      <div class="password-input-container">
        <van-field
            v-model="password"
            type="password"
            placeholder="请输入队伍密码"
            label="密码"
            label-width="60px"
            :border="false"
            class="password-field"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { TeamType } from "../models/team";
import { teamStatusEnum } from "../constants/team";
import ikun from '../assets/ikun.png';
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import { onMounted, ref } from "vue";
import { getCurrentUser } from "../services/user";
import { useRouter } from "vue-router";
import teamAvatar from "../assets/OIP.jpg";

interface TeamCardListProps {
  teamList: TeamType[];
}

const props = withDefaults(defineProps<TeamCardListProps>(), {
  teamList: () => [] as TeamType[],
});

const showPasswordDialog = ref(false);
const password = ref('');
const joinTeamId = ref(0);
const currentUser = ref();

const router = useRouter();

onMounted(async () => {
  currentUser.value = await getCurrentUser();
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 跳转到团队详情页
const goToTeamDetail = (teamId: number) => {
  router.push({
    path: '/team/detail',
    query: {
      id: teamId
    }
  });
};

const preJoinTeam = (team: TeamType) => {
  joinTeamId.value = team.id;
  if (team.status === 0) {
    // 公开队伍，直接加入
    doJoinTeam();
  } else if (team.status === 1) {
    // 加密队伍，需要密码
    showPasswordDialog.value = true;
  } else {
    // 私有队伍，不允许直接加入
    Toast.fail('私有队伍仅限邀请加入');
  }
};

const doJoinCancel = () => {
  joinTeamId.value = 0;
  password.value = '';
};

/**
 * 加入队伍
 */
const doJoinTeam = async () => {
  if (!joinTeamId.value) {
    return;
  }
  const res = await myAxios.post('/team/join', {
    teamId: joinTeamId.value,
    password: password.value
  });
  if (res?.code === 0) {
    Toast.success('加入成功');
    doJoinCancel();
    showPasswordDialog.value = false;
    // 刷新页面数据
    window.location.reload();
  } else {
    Toast.fail('加入失败' + (res.description ? `，${res.description}` : ''));
  }
};

/**
 * 跳转至更新队伍页
 * @param id
 */
const doUpdateTeam = (id: number) => {
  router.push({
    path: '/team/update',
    query: {
      id,
    }
  });
};
</script>

<style scoped>
.team-card-list {
  padding: 16px;
  background: #f5f7fa;
  min-height: 100vh;
}

.team-card {
  background: #ffffff;
  border-radius: 20px;
  margin-bottom: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f1f5f9;
  position: relative;
  overflow: hidden;
}

.team-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.card-header {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.team-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  object-fit: cover;
  border: 3px solid #f1f5f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.member-count {
  position: absolute;
  bottom: -8px;
  right: -8px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 700;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.team-info {
  flex: 1;
  min-width: 0;
}

.title-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  word-break: break-word;
}

.creator-badge {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  border: none;
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.team-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0 0 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.team-status {
  margin-bottom: 8px;
}

.status-tag {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #64748b;
}

.info-icon {
  font-size: 14px;
  color: #3b82f6;
  width: 16px;
  flex-shrink: 0;
}

.card-footer {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.detail-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 600;
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.detail-btn:hover {
  background: rgba(59, 130, 246, 0.1);
}

.quick-join-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.quick-join-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.quick-edit-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 600;
  border-color: #f59e0b;
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.quick-edit-btn:hover {
  background: rgba(245, 158, 11, 0.1);
}

.joined-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #10b981;
  background: #dcfce7;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 600;
  border: 1px solid #bbf7d0;
}

/* 密码对话框样式 */
.password-input-container {
  padding: 20px 0;
}

.password-field {
  background: #f8fafc;
  border-radius: 12px;
}

:deep(.password-dialog .van-dialog__content) {
  padding: 0 24px;
}

:deep(.password-field .van-field__control) {
  background: transparent;
}

:deep(.password-field .van-field__body) {
  background: #f8fafc;
  border-radius: 12px;
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-card-list {
    padding: 12px;
  }

  .team-card {
    padding: 16px;
  }

  .card-header {
    gap: 12px;
  }

  .avatar-image {
    width: 60px;
    height: 60px;
  }

  .team-name {
    font-size: 16px;
  }

  .card-footer {
    gap: 6px;
  }

  .title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 卡片动画效果 */
@keyframes cardIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.team-card {
  animation: cardIn 0.5s ease-out;
}

/* 渐变背景动画 */
.team-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.team-card:hover::after {
  left: 100%;
}
</style>