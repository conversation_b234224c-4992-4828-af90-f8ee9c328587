<template>
  <div id="teamCardList">
    <van-card
        v-for="team in props.teamList"
        :key="team.id"
        :desc="team.description"
        :title="team.name"
        :thumb="ikun"
        thumb-link="#"
        class="team-card"
    >
      <template #thumb>
        <div class="thumb-container">
          <img :src="ikun" class="thumb-image" alt="团队头像" />
          <div class="member-count">
            {{ team.hasJoinNum }}/{{ team.maxNum }}人
          </div>
        </div>
      </template>

      <template #title>
        <div class="title-container">
          <div class="team-name">
            <span class="name">{{ team.name }}</span>
            <!-- 创建者标识 -->
            <van-tag
                v-if="team.userId === currentUser?.id"
                type="success"
                size="small"
                class="creator-tag"
            >
              <van-icon name="crown-o" />
              队长
            </van-tag>
          </div>
          <van-tag
              :type="team.status === 0 ? 'success' : 'warning'"
              size="medium"
              class="status-tag"
          >
            {{ teamStatusEnum[team.status] }}
          </van-tag>
        </div>
      </template>

      <template #tags>
        <div class="team-info">
          <div class="info-row">
            <van-icon name="friends-o" class="info-icon" />
            <span>队伍人数: {{ team.hasJoinNum }}/{{ team.maxNum }}</span>
          </div>
          <div class="info-row" v-if="team.expireTime">
            <van-icon name="clock-o" class="info-icon" />
            <span>过期时间: {{ team.expireTime }}</span>
          </div>
          <div class="info-row">
            <van-icon name="calendar-o" class="info-icon" />
            <span>创建时间: {{ team.createTime }}</span>
          </div>
        </div>
      </template>

      <template #footer>
        <div class="footer-container">
          <div class="button-group">
            <!-- 查看详情按钮 - 所有人都可见 -->
            <van-button
                type="primary"
                size="small"
                plain
                round
                class="detail-btn"
                @click="goToTeamDetail(team.id)"
            >
              <van-icon name="eye-o" />
              查看详情
            </van-button>

            <!-- 快速加入按钮 - 非队长且未加入时可见 -->
            <van-button
                v-if="team.userId !== currentUser?.id && !team.hasJoin"
                type="primary"
                size="small"
                round
                class="quick-join-btn"
                @click="preJoinTeam(team)"
            >
              <van-icon name="plus" />
              快速加入
            </van-button>

            <!-- 队长快速操作 -->
            <van-button
                v-if="team.userId === currentUser?.id"
                type="primary"
                size="small"
                plain
                round
                class="quick-edit-btn"
                @click="doUpdateTeam(team.id)"
            >
              <van-icon name="edit" />
              编辑
            </van-button>

            <!-- 已加入标识 -->
            <van-tag
                v-if="team.userId !== currentUser?.id && team.hasJoin"
                type="success"
                size="medium"
                class="joined-tag"
            >
              <van-icon name="success" />
              已加入
            </van-tag>
          </div>
        </div>
      </template>
    </van-card>

    <!-- 密码对话框 -->
    <van-dialog
        v-model:show="showPasswordDialog"
        title="请输入队伍密码"
        :show-cancel-button="true"
        confirm-button-text="加入"
        cancel-button-text="取消"
        class="password-dialog"
        @confirm="doJoinTeam"
        @cancel="doJoinCancel"
    >
      <div class="password-input-container">
        <van-field
            v-model="password"
            type="password"
            placeholder="请输入队伍密码"
            label="密码"
            label-width="60px"
            :border="false"
            class="password-field"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { TeamType } from "../models/team";
import { teamStatusEnum } from "../constants/team";
import ikun from '../assets/ikun.png';
import myAxios from "../plugins/myAxios";
import { Dialog, Toast } from "vant";
import { onMounted, ref } from "vue";
import { getCurrentUser } from "../services/user";
import { useRouter } from "vue-router";

interface TeamCardListProps {
  teamList: TeamType[];
}

const props = withDefaults(defineProps<TeamCardListProps>(), {
  teamList: () => [] as TeamType[],
});

const showPasswordDialog = ref(false);
const password = ref('');
const joinTeamId = ref(0);
const currentUser = ref();

const router = useRouter();

onMounted(async () => {
  currentUser.value = await getCurrentUser();
});

// 跳转到团队详情页
const goToTeamDetail = (teamId: number) => {
  router.push({
    path: '/team/detail',
    query: {
      id: teamId
    }
  });
};

const preJoinTeam = (team: TeamType) => {
  joinTeamId.value = team.id;
  if (team.status === 0) {
    doJoinTeam();
  } else {
    showPasswordDialog.value = true;
  }
};

const doJoinCancel = () => {
  joinTeamId.value = 0;
  password.value = '';
};

/**
 * 加入队伍
 */
const doJoinTeam = async () => {
  if (!joinTeamId.value) {
    return;
  }
  const res = await myAxios.post('/team/join', {
    teamId: joinTeamId.value,
    password: password.value
  });
  if (res?.code === 0) {
    Toast.success('加入成功');
    doJoinCancel();
    // 刷新页面数据
    window.location.reload();
  } else {
    Toast.fail('加入失败' + (res.description ? `，${res.description}` : ''));
  }
};

/**
 * 跳转至更新队伍页
 * @param id
 */
const doUpdateTeam = (id: number) => {
  router.push({
    path: '/team/update',
    query: {
      id,
    }
  });
};
</script>

<style scoped>
.team-card {
  margin-bottom: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: #ffffff;
  transition: all 0.3s ease;
  overflow: hidden;
}

.team-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.thumb-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
}

.thumb-image {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  object-fit: cover;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.member-count {
  font-size: 11px;
  color: #1e40af;
  text-align: center;
  margin-top: 6px;
  background: #dbeafe;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.team-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.creator-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

.status-tag {
  font-size: 11px;
  border-radius: 12px;
  flex-shrink: 0;
}

.team-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  margin-top: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #64748b;
}

.info-icon {
  font-size: 14px;
  color: #3b82f6;
  width: 16px;
  flex-shrink: 0;
}

.footer-container {
  margin-top: 12px;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.detail-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
  border-color: #3b82f6;
  color: #3b82f6;
}

.quick-join-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  color: white;
}

.quick-edit-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
  border-color: #f59e0b;
  color: #f59e0b;
}

.joined-tag {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  background: #dcfce7;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

/* 重写van-card样式 */
:deep(.van-card) {
  border: none;
  box-shadow: none;
  background: transparent;
}

:deep(.van-card__header) {
  padding: 16px 16px 8px 16px;
  background: #ffffff;
}

:deep(.van-card__body) {
  padding: 0 16px;
  background: #ffffff;
}

:deep(.van-card__footer) {
  padding: 8px 16px 16px 16px;
  background: #ffffff;
}

:deep(.van-card__desc) {
  color: #64748b;
  font-size: 14px;
  line-height: 1.4;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 密码对话框样式 */
.password-input-container {
  padding: 20px 0;
}

.password-field {
  background: #f8fafc;
  border-radius: 8px;
}

:deep(.password-dialog .van-dialog__content) {
  padding: 0 24px;
}

:deep(.password-field .van-field__control) {
  background: transparent;
}

:deep(.password-field .van-field__body) {
  background: #f8fafc;
  border-radius: 8px;
}
</style>
<!--去掉内框，如果是自己创建的给出相应的美观标识，并且，单击详细时会跳转另一个界面，该界面详细展示了队伍信息，在上二分之一的部分展示队伍的头像，名字，描述，（有背景图片占一定的比例），下面二分之一展示了加入改队伍的用户卡片（根据后端传入的 List<Long> hasJoinUsersid 数据，前端调用@GetMapping("/search/ids") public BaseResponse<List<User>> searchUserIds(@RequestParam(required = false) List<Long> tagNameList){），加入队伍和更改队伍，解散队伍的按钮也在这个界面，布局合理-->