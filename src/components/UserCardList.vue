<template>
  <div>
    <van-skeleton v-if="props.loading" title avatar :row="3" />
    <div v-else>
      <van-card
          v-for="user in props.userList"
          :key="user.id"
          :desc="user.profile"
          :title="user.username"
          :thumb="user.avatarUrl"
          thumb-link="#"
          class="user-card"
      >
        <template #thumb>
          <div class="thumb-container">
            <img v-if="user.avatarUrl" :src="user.avatarUrl" class="thumb-image" />
            <van-icon v-else name="photo" class="thumb-icon" />
            <div class="join-days">
              来到圈友已经 {{ calculateJoinDays(user.createTime) }} 天
            </div>
          </div>
        </template>

        <template #title>
          <div class="title-container">
            <div class="name-gender">
              <span class="username">{{ user.username }}</span>
              <span class="gender" v-if="user.gender !== undefined">
                {{ user.gender === 0 ? '♀' : '♂' }}
              </span>
            </div>
            <van-button
                plain
                icon="https://fastly.jsdelivr.net/npm/@vant/assets/user-active.png"
                type="primary"
                size="mini"
                class="contact-btn"
                @click="showContactInfo(user)"
            >
              联系我
            </van-button>
          </div>
        </template>

        <!-- 这里用简约的 van-tag 替换了原来的自定义标签样式 -->
        <template #tags>
          <div class="tag-list">
            <van-tag
                v-for="(tag, index) in user.tags"
                :key="index"
                type="primary"
                size="medium"
                class="tag-item"
            >
              {{ tag }}
            </van-tag>
          </div>
        </template>

        <template #footer>
          <div class="footer-container">
            <!-- 原来的按钮已移动到title区域 -->
          </div>
        </template>
      </van-card>
    </div>

    <van-dialog
        v-model:show="showContactDialog"
        title="联系方式"
        :show-cancel-button="false"
        confirm-button-text="确定"
        class="contact-dialog"
    >
      <div class="contact-info">
        <div class="contact-item" v-if="selectedUser?.phone">
          <van-icon name="phone-o" class="contact-icon" />
          <div class="contact-content">
            <div class="contact-label">手机号</div>
            <div class="contact-value">{{ selectedUser.phone }}</div>
          </div>
        </div>

        <div class="contact-item" v-if="selectedUser?.email">
          <van-icon name="envelop-o" class="contact-icon" />
          <div class="contact-content">
            <div class="contact-label">邮箱</div>
            <div class="contact-value">{{ selectedUser.email }}</div>
          </div>
        </div>

        <div v-if="!selectedUser?.phone && !selectedUser?.email" class="no-contact">
          <van-icon name="info-o" class="no-contact-icon" />
          <div class="no-contact-text">暂无联系方式</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { UserType } from '../models/user';

interface UserCardListProps {
  loading: boolean;
  userList: UserType[];
}

const props = withDefaults(defineProps<UserCardListProps>(), {
  loading: true,
  userList: () => [] as UserType[],
});

const showContactDialog = ref(false);
const selectedUser = ref<UserType | null>(null);

const showContactInfo = (user: UserType) => {
  selectedUser.value = user;
  showContactDialog.value = true;
};

const calculateJoinDays = (createTime: string | number | Date): number => {
  if (!createTime) return 0;
  const createDate = new Date(createTime);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - createDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};
</script>

<style scoped>
.user-card {
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: #ffffff;
  transition: all 0.3s ease;
  overflow: hidden;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.thumb-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
}

.thumb-image {
  width: 70px;
  height: 70px;
  border-radius: 12px;
  object-fit: cover;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thumb-icon {
  width: 70px;
  height: 70px;
  font-size: 40px;
  color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.join-days {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: 8px;
  line-height: 1.2;
  width: 80px;
  font-weight: 500;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
}

.name-gender {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-size: 17px;
  font-weight: 600;
  color: #1a1a1a;
}

.gender {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b6b;
}

.contact-btn {
  font-size: 11px;
  padding: 6px 12px;
  min-width: auto;
  flex-shrink: 0;
  border-radius: 20px;
  font-weight: 500;
}

.footer-container {
  position: relative;
}

/* 简约标签容器 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 12px;
  border: 1px solid #e5e5e5;
  margin-top: 12px;
}

.tag-item {
  font-size: 13px;
  border-radius: 14px;
  padding: 6px 12px;
}

/* 重写van-card样式，保持原样 */
:deep(.van-card) {
  border: none;
  box-shadow: none;
  background: transparent;
}

:deep(.van-card__header) {
  padding: 20px 20px 12px 20px;
  background: #ffffff;
}

:deep(.van-card__body) {
  padding: 0 20px;
  background: #ffffff;
}

:deep(.van-card__footer) {
  padding: 12px 20px 20px 20px;
  background: #ffffff;
}

:deep(.van-card__desc) {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 8px;
}

.contact-info {
  padding: 20px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  font-size: 20px;
  color: #1989fa;
  margin-right: 12px;
  width: 24px;
  flex-shrink: 0;
}

.contact-content {
  flex: 1;
}

.contact-label {
  font-size: 14px;
  color: #969799;
  margin-bottom: 4px;
}

.contact-value {
  font-size: 16px;
  color: #323233;
  font-weight: 500;
}

.no-contact {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  color: #969799;
}

.no-contact-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.no-contact-text {
  font-size: 14px;
}

:deep(.contact-dialog .van-dialog__content) {
  padding: 0 24px;
}
</style>
