<template>
  <van-nav-bar
      :title="title"
      left-arrow
      @click-left="onClickLeft"
      @click-right="onClickRight"
  >
    <template #right v-if="showSearch">
      <van-icon name="search" size="18"/>
    </template>
  </van-nav-bar>

  <div id="content" :class="{ 'no-tabbar': !showTabbar }">
    <router-view/>
  </div>

  <van-tabbar v-if="showTabbar" route @change="onChange">
    <van-tabbar-item to="/" icon="home-o" name="index">主页</van-tabbar-item>
    <van-tabbar-item to="/team" icon="search" name="team">队伍</van-tabbar-item>
    <van-tabbar-item to="/user" icon="friends-o" name="user">个人</van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { ref, computed } from "vue";
import routes from "../config/route";
import { Dialog } from "vant";

const router = useRouter();
const DEFAULT_TITLE = '圈友';
const title = ref(DEFAULT_TITLE);

const hiddenLayoutPaths = ['/user/login', '/user/register', '/user/EdittagsPage'];

const showTabbar = computed(() => {
  return !hiddenLayoutPaths.includes(router.currentRoute.value.path);
});

const showSearch = computed(() => {
  return !hiddenLayoutPaths.includes(router.currentRoute.value.path);
});

router.beforeEach((to, from) => {
  const toPath = to.path;
  const route = routes.find((route) => {
    return toPath == route.path;
  });
  title.value = route?.title ?? DEFAULT_TITLE;
});

const onClickRight = () => {
  router.push('/search');
};

const onChange = (name: string) => {
  console.log('切换到标签:', name);
};

const onClickLeft = () => {
  if (router.currentRoute.value.path === '/user/login') {
    Dialog.confirm({
      title: '退出确认',
      message: '确定要退出网站吗？',
    }).then(() => {
      window.close();
    });
  } else {
    router.back();
  }
};
</script>

<style scoped>
/* 确保页面有正确的布局 */
#content {
  position: fixed;
  top: 46px; /* 导航栏高度 */
  left: 0;
  right: 0;
  bottom: 50px; /* 标签栏高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
}

#content.no-tabbar {
  bottom: 0;
}

/* 重置一些可能影响滚动的样式 */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

#app {
  height: 100vh;
  overflow: hidden;
}
</style>