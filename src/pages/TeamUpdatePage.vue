<template>
  <div id="teamUpdatePage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">编辑队伍</h1>
        <p class="page-subtitle">修改队伍信息和设置</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" size="24px" vertical>
      加载队伍信息...
    </van-loading>

    <!-- 表单内容 -->
    <div v-else class="form-container">
      <van-form @submit="onSubmit" class="team-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">
            <van-icon name="edit" />
            基本信息
          </h3>
          <van-cell-group inset class="form-group">
            <van-field
                v-model="addTeamData.name"
                name="name"
                label="队伍名称"
                placeholder="请输入队伍名称"
                :rules="[{ required: true, message: '请输入队伍名称' }]"
                left-icon="flag-o"
                class="form-field"
            />
            <van-field
                v-model="addTeamData.description"
                rows="4"
                autosize
                label="队伍描述"
                type="textarea"
                placeholder="请描述队伍的目标和特色"
                left-icon="notes-o"
                class="form-field"
            />
          </van-cell-group>
        </div>

        <!-- 队伍设置 -->
        <div class="form-section">
          <h3 class="section-title">
            <van-icon name="setting-o" />
            队伍设置
          </h3>
          <van-cell-group inset class="form-group">
            <van-field
                is-link
                readonly
                name="datetimePicker"
                label="过期时间"
                :placeholder="formatExpireTime(addTeamData.expireTime) || '点击选择过期时间'"
                @click="showPicker = true"
                left-icon="clock-o"
                class="form-field"
            />
            <van-field name="stepper" label="最大人数" left-icon="friends-o" class="form-field">
              <template #input>
                <van-stepper
                  v-model="addTeamData.maxNum"
                  max="10"
                  min="3"
                  button-size="28px"
                  input-width="50px"
                />
              </template>
            </van-field>
          </van-cell-group>
        </div>

        <!-- 隐私设置 -->
        <div class="form-section">
          <h3 class="section-title">
            <van-icon name="shield-o" />
            隐私设置
          </h3>
          <van-cell-group inset class="form-group">
            <van-field name="radio" label="队伍状态" class="form-field">
              <template #input>
                <div class="status-options">
                  <div
                    v-for="option in statusOptions"
                    :key="option.value"
                    :class="['status-option', { active: addTeamData.status == option.value }]"
                    @click="addTeamData.status = option.value"
                  >
                    <van-icon :name="option.icon" class="option-icon" />
                    <span class="option-text">{{ option.label }}</span>
                    <span class="option-desc">{{ option.desc }}</span>
                  </div>
                </div>
              </template>
            </van-field>
            <van-field
                v-if="Number(addTeamData.status) === 1"
                v-model="addTeamData.password"
                type="password"
                name="password"
                label="队伍密码"
                placeholder="请输入6-20位密码"
                :rules="[{ required: true, message: '请填写密码' }]"
                left-icon="lock"
                class="form-field password-field"
            />
          </van-cell-group>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            class="submit-btn"
            :loading="submitting"
          >
            <van-icon name="success" />
            保存修改
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 时间选择器弹窗 -->
    <van-popup v-model:show="showPicker" position="bottom" class="datetime-popup">
      <van-datetime-picker
          v-model="addTeamData.expireTime"
          @confirm="confirmDateTime"
          @cancel="showPicker = false"
          type="datetime"
          title="请选择过期时间"
          :min-date="minDate"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">

import {useRoute, useRouter} from "vue-router";
import {onMounted, ref} from "vue";
import myAxios from "../plugins/myAxios";
import {Toast} from "vant";

const router = useRouter();
const route = useRoute();

// 展示日期选择器
const showPicker = ref(false);

const minDate = new Date();

const id = route.query.id;

// 需要用户填写的表单数据
const addTeamData = ref({})

// 获取之前的队伍信息
onMounted(async () => {
  if (id <= 0) {
    Toast.fail('加载队伍失败');
    return;
  }
  const res = await myAxios.get("/team/get", {
    params: {
      id,
    }
  });
  if (res?.code === 0) {
    addTeamData.value = res.data;
  } else {
    Toast.fail('加载队伍失败，请刷新重试');
  }}
)

// 提交
const onSubmit = async () => {
  const postData = {
    ...addTeamData.value,
    status: Number(addTeamData.value.status)
  }
  // todo 前端参数校验
  const res = await myAxios.post("/team/update", postData);
  if (res?.code === 0 && res.data){
    Toast.success('更新成功');
    router.push({
      path: '/team',
      replace: true,
    });
  } else {
    Toast.success('更新失败');
  }
}
</script>

<style scoped>
#teamPage {

}
</style>
