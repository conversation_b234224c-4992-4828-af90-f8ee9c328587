<template>
  <div id="teamUpdatePage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">编辑队伍</h1>
        <p class="page-subtitle">修改队伍信息和设置</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" size="24px" vertical>
      加载队伍信息...
    </van-loading>

    <!-- 表单内容 -->
    <div v-else class="form-container">
      <van-form @submit="onSubmit" class="team-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">
            <van-icon name="edit" />
            基本信息
          </h3>
          <van-cell-group inset class="form-group">
            <van-field
                v-model="addTeamData.name"
                name="name"
                label="队伍名称"
                placeholder="请输入队伍名称"
                :rules="[{ required: true, message: '请输入队伍名称' }]"
                left-icon="flag-o"
                class="form-field"
            />
            <van-field
                v-model="addTeamData.description"
                rows="4"
                autosize
                label="队伍描述"
                type="textarea"
                placeholder="请描述队伍的目标和特色"
                left-icon="notes-o"
                class="form-field"
            />
          </van-cell-group>
        </div>

        <!-- 队伍设置 -->
        <div class="form-section">
          <h3 class="section-title">
            <van-icon name="setting-o" />
            队伍设置
          </h3>
          <van-cell-group inset class="form-group">
            <van-field
                is-link
                readonly
                name="datetimePicker"
                label="过期时间"
                :placeholder="formatExpireTime(addTeamData.expireTime) || '点击选择过期时间'"
                @click="showPicker = true"
                left-icon="clock-o"
                class="form-field"
            />
            <van-field name="stepper" label="最大人数" left-icon="friends-o" class="form-field">
              <template #input>
                <van-stepper
                  v-model="addTeamData.maxNum"
                  max="10"
                  min="3"
                  button-size="28px"
                  input-width="50px"
                />
              </template>
            </van-field>
          </van-cell-group>
        </div>

        <!-- 隐私设置 -->
        <div class="form-section">
          <h3 class="section-title">
            <van-icon name="shield-o" />
            隐私设置
          </h3>
          <van-cell-group inset class="form-group">
            <van-field name="radio" label="队伍状态" class="form-field">
              <template #input>
                <div class="status-options">
                  <div
                    v-for="option in statusOptions"
                    :key="option.value"
                    :class="['status-option', { active: addTeamData.status == option.value }]"
                    @click="addTeamData.status = option.value"
                  >
                    <van-icon :name="option.icon" class="option-icon" />
                    <span class="option-text">{{ option.label }}</span>
                    <span class="option-desc">{{ option.desc }}</span>
                  </div>
                </div>
              </template>
            </van-field>
            <van-field
                v-if="Number(addTeamData.status) === 1"
                v-model="addTeamData.password"
                type="password"
                name="password"
                label="队伍密码"
                placeholder="请输入6-20位密码"
                :rules="[{ required: true, message: '请填写密码' }]"
                left-icon="lock"
                class="form-field password-field"
            />
          </van-cell-group>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            class="submit-btn"
            :loading="submitting"
          >
            <van-icon name="success" />
            保存修改
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 时间选择器弹窗 -->
    <van-popup v-model:show="showPicker" position="bottom" class="datetime-popup">
      <van-datetime-picker
          v-model="addTeamData.expireTime"
          @confirm="confirmDateTime"
          @cancel="showPicker = false"
          type="datetime"
          title="请选择过期时间"
          :min-date="minDate"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">

import {useRoute, useRouter} from "vue-router";
import {onMounted, ref} from "vue";
import myAxios from "../plugins/myAxios";
import {Toast} from "vant";

const router = useRouter();
const route = useRoute();

// 展示日期选择器
const showPicker = ref(false);
const loading = ref(true);
const submitting = ref(false);

const minDate = new Date();

const id = route.query.id;

// 状态选项配置 - 修正状态值：公开0，加密1，私有2
const statusOptions = [
  {
    value: 0,
    label: '公开',
    desc: '所有人可见可加入',
    icon: 'eye-o'
  },
  {
    value: 1,
    label: '加密',
    desc: '需要密码才能加入',
    icon: 'lock'
  },
  {
    value: 2,
    label: '私有',
    desc: '仅邀请用户可加入',
    icon: 'shield-o'
  }
];

// 需要用户填写的表单数据
const addTeamData = ref({
  name: '',
  description: '',
  expireTime: null,
  maxNum: 3,
  password: '',
  status: 0
});

// 格式化过期时间显示
const formatExpireTime = (time: Date | string | null) => {
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 确认时间选择
const confirmDateTime = () => {
  showPicker.value = false;
};

// 表单验证
const validateForm = () => {
  if (!addTeamData.value.name?.trim()) {
    Toast.fail('请输入队伍名称');
    return false;
  }

  if (addTeamData.value.name.length > 20) {
    Toast.fail('队伍名称不能超过20个字符');
    return false;
  }

  if (addTeamData.value.description && addTeamData.value.description.length > 200) {
    Toast.fail('队伍描述不能超过200个字符');
    return false;
  }

  if (Number(addTeamData.value.status) === 1 && !addTeamData.value.password) {
    Toast.fail('加密队伍需要设置密码');
    return false;
  }

  if (addTeamData.value.password && (addTeamData.value.password.length < 6 || addTeamData.value.password.length > 20)) {
    Toast.fail('密码长度应为6-20位');
    return false;
  }

  return true;
};

// 获取之前的队伍信息
onMounted(async () => {
  if (!id || id <= 0) {
    Toast.fail('队伍ID无效');
    router.back();
    return;
  }

  try {
    loading.value = true;
    const res = await myAxios.get("/team/get", {
      params: {
        id,
      }
    });

    if (res?.code === 0 && res.data) {
      // 处理日期格式
      if (res.data.expireTime) {
        res.data.expireTime = new Date(res.data.expireTime);
      }
      addTeamData.value = {
        ...res.data,
        // 确保必要字段有默认值
        maxNum: res.data.maxNum || 3,
        status: res.data.status ?? 0,
        password: res.data.password || ''
      };
    } else {
      Toast.fail(res?.description || '加载队伍失败');
      router.back();
    }
  } catch (error) {
    console.error('加载队伍信息失败:', error);
    Toast.fail('加载失败，请检查网络连接');
    router.back();
  } finally {
    loading.value = false;
  }
});

// 提交
const onSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  submitting.value = true;
  try {
    const postData = {
      ...addTeamData.value,
      status: Number(addTeamData.value.status)
    }

    const res = await myAxios.post("/team/update", postData);
    if (res?.code === 0) {
      Toast.success('更新成功');
      router.push({
        path: '/team',
        replace: true,
      });
    } else {
      Toast.fail(res?.description || '更新失败');
    }
  } catch (error) {
    console.error('更新队伍失败:', error);
    Toast.fail('更新失败，请检查网络连接');
  } finally {
    submitting.value = false;
  }
}
</script>

<style scoped>
#teamUpdatePage {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 页面头部 */
.page-header {
  padding: 40px 20px 30px;
  text-align: center;
  color: white;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 2;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* 加载容器 */
.loading-container {
  background: #f5f7fa;
  border-radius: 24px 24px 0 0;
  margin-top: -12px;
  position: relative;
  z-index: 3;
  min-height: calc(100vh - 150px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1989fa;
}

/* 表单容器 */
.form-container {
  background: #f5f7fa;
  border-radius: 24px 24px 0 0;
  margin-top: -12px;
  position: relative;
  z-index: 3;
  min-height: calc(100vh - 150px);
  padding: 24px 0 40px;
}

.team-form {
  padding: 0 16px;
}

/* 表单分组 */
.form-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 16px;
}

.section-title .van-icon {
  color: #1989fa;
  font-size: 18px;
}

.form-group {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.form-field {
  background: white;
}

:deep(.form-field .van-field__label) {
  font-weight: 500;
  color: #323233;
}

:deep(.form-field .van-field__left-icon) {
  color: #1989fa;
  margin-right: 8px;
}

/* 状态选择样式 */
.status-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.status-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 2px solid #ebedf0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.status-option.active {
  border-color: #1989fa;
  background: #e8f4fd;
}

.option-icon {
  font-size: 20px;
  color: #969799;
  margin-right: 12px;
  transition: color 0.3s ease;
}

.status-option.active .option-icon {
  color: #1989fa;
}

.option-text {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-right: 8px;
}

.option-desc {
  font-size: 12px;
  color: #969799;
  flex: 1;
}

.status-option.active .option-text {
  color: #1989fa;
}

/* 密码字段动画 */
.password-field {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 提交按钮 */
.submit-section {
  margin: 32px 16px 16px;
}

.submit-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border: none;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.submit-btn .van-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 时间选择器弹窗 */
.datetime-popup {
  border-radius: 16px 16px 0 0;
}

:deep(.datetime-popup .van-datetime-picker) {
  background: white;
}

:deep(.datetime-popup .van-picker__toolbar) {
  background: #f7f8fa;
}

/* 步进器样式优化 */
:deep(.van-stepper) {
  background: transparent;
}

:deep(.van-stepper__minus),
:deep(.van-stepper__plus) {
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 6px;
}

:deep(.van-stepper__input) {
  background: #f7f8fa;
  border: 1px solid #ebedf0;
  border-radius: 6px;
  text-align: center;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 30px 16px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .form-container {
    padding: 20px 0 30px;
  }

  .team-form {
    padding: 0 12px;
  }

  .section-title {
    margin-left: 12px;
    font-size: 15px;
  }

  .submit-section {
    margin: 24px 12px 12px;
  }

  .submit-btn {
    height: 46px;
    font-size: 15px;
  }

  .status-option {
    padding: 10px 12px;
  }

  .option-text {
    font-size: 15px;
  }

  .option-desc {
    font-size: 11px;
  }
}
</style>
