<template>
  <template v-if="user">
    <van-cell
        title="昵称"
        is-link
        to="/user/edit"
        :value="user.username"
        @click="toEdit('username', '昵称', user.username)"
    />
    <van-cell title="账号" :value="user.userAccount" />

    <!-- 修改后的头像上传 -->
    <van-cell title="头像" is-link @click="onUploadAvatar">
      <van-image
          round
          fit="cover"
          width="5rem"
          height="5rem"
          :src="user.avatarUrl"
      />
    </van-cell>

    <!-- 隐藏的 uploader -->
    <van-uploader
        v-model="fileList"
        :after-read="uploadAvatar"
        :max-count="1"
        accept="image/*"
        style="display: none"
        ref="uploaderRef"
    />

    <van-cell
        title="性别"
        is-link
        :value="user.gender === 1 ? '男' : user.gender === 0 ? '女' : ''"
        @click="toEdit('gender', '性别', user.gender)"
    />
    <van-cell
        title="电话"
        is-link
        to="/user/edit"
        :value="user.phone"
        @click="toEdit('phone', '电话', user.phone)"
    />
    <van-cell
        title="邮箱"
        is-link
        to="/user/edit"
        :value="user.email"
        @click="toEdit('email', '邮箱', user.email)"
    />

    <!-- 新增标签编辑 -->
    <van-cell
        title="标签"
        is-link
        :value="formatTags(user.tags)"
        @click="toEditTags"
    />

    <van-cell title="编号" :value="user.planetCode" />
    <van-cell title="注册时间" :value="user.createTime" />

    <!-- 修改密码 -->
    <van-cell title="修改密码" is-link @click="toChangePassword" />
  </template>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { getCurrentUser } from "../services/user";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";

const user = ref<any>(null);
const router = useRouter();

onMounted(async () => {
  user.value = await getCurrentUser();
});

const toEdit = (editKey: string, editName: string, currentValue: any) => {
  const safeValue = typeof currentValue === "string" || typeof currentValue === "number"
      ? currentValue.toString()
      : "";
  router.push({
    path: "/user/edit",
    query: { editKey, editName, currentValue: safeValue},
  });
};

// 格式化标签显示
const formatTags = (tags: string | null) => {
  if (!tags) return "未设置";
  try {
    const tagArray = JSON.parse(tags);
    return Array.isArray(tagArray) ? tagArray.join(", ") : "未设置";
  } catch {
    return "未设置";
  }
};

// 跳转到标签编辑页面
const toEditTags = () => {
  router.push({
    path: "/user/EdittagsPage",
    query: {
      currentTags: user.value.tags || "[]"
    }
  });
};

const toChangePassword = () => {
  router.push("/user/ChangePassword");
};

// 头像上传逻辑
const fileList = ref<any[]>([]);
const uploaderRef = ref();

const onUploadAvatar = () => {
  uploaderRef.value?.chooseFile?.(); // 手动触发文件选择
};

const uploadAvatar = async (file: any) => {
  const formData = new FormData();
  formData.append("file", file.file); // file.file 是原始文件对象

  try {
    const uploadRes = await myAxios.post("user/upload/avatar", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    if (uploadRes.code === 0 && uploadRes.data) {
      const avatarUrl = uploadRes.data;

      // 更新用户头像
      const res = await myAxios.post("/user/update", {
        id: user.value.id,
        avatarUrl: avatarUrl,
      });

      if (res.code === 0 && res.data > 0) {
        user.value.avatarUrl = avatarUrl;
        Toast.success("头像上传成功");
      } else {
        Toast.fail("头像更新失败");
      }
    } else {
      Toast.fail("上传失败");
    }
  } catch (error) {
    Toast.fail("上传出错");
  }
};
</script>

<style scoped>
img {
  object-fit: cover;
}
</style>