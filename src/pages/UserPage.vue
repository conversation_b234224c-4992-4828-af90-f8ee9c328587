<template>
  <template v-if="user">
    <van-cell title="当前用户" :value="user?.username" />
    <van-cell title="修改信息" is-link to="/user/update" />
    <van-cell title="我创建的队伍" is-link to="/user/team/create" />
    <van-cell title="我加入的队伍" is-link to="/user/team/join" />

    <!-- 退出登录按钮 -->
    <div style="margin: 16px;">
      <van-button round block type="primary" @click="logout">
        退出登录
      </van-button>
    </div>
  </template>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import { getCurrentUser } from "../services/user";

const user = ref();
const router = useRouter();

onMounted(async () => {
  user.value = await getCurrentUser();
});

// 跳转编辑页面函数（如果后续需要使用）
const toEdit = (editKey: string, editName: string, currentValue: string) => {
  router.push({
    path: '/user/edit',
    query: {
      editKey,
      editName,
      currentValue,
    }
  });
};

// 退出登录逻辑
const logout = async () => {
  try {
    const res = await myAxios.post('/user/logout');
    if (res.code === 0) {
      Toast.success('已退出登录');
      router.replace('/user/login'); // 跳转到登录页
    } else {
      Toast.fail(res.message || '退出失败');
    }
  } catch (e) {
    Toast.fail('网络异常，退出失败');
  }
};
</script>

<style scoped>
</style>
