<template>
  <div class="user-page">
    <!-- 加载状态 -->
    <van-loading v-if="!user" class="loading-container" size="24px" vertical>
      加载中...
    </van-loading>

    <template v-if="user">
      <!-- 用户信息卡片 -->
      <div class="user-info-card">
        <div class="user-header">
          <div class="user-avatar">
            <img v-if="user.avatarUrl" :src="user.avatarUrl" alt="头像" class="avatar-image" />
            <van-icon v-else name="contact" class="default-avatar" />
          </div>
          <div class="user-basic-info">
            <h2 class="username">{{ user.username || '未设置用户名' }}</h2>
            <p class="user-account">账号：{{ user.userAccount || '未设置' }}</p>
            <p class="user-code">编号：{{ user.planetCode || '未设置' }}</p>
          </div>
        </div>

        <!-- 用户详细信息 -->
        <div class="user-details">
          <div class="detail-item">
            <span class="detail-label">性别</span>
            <span class="detail-value">{{ getGenderText(user.gender) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">邮箱</span>
            <span class="detail-value">{{ user.email || '未设置' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">电话</span>
            <span class="detail-value">{{ user.phone || '未设置' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">个人简介</span>
            <span class="detail-value">{{ user.profile || '这个人很懒，什么都没留下' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">注册时间</span>
            <span class="detail-value">{{ formatDate(user.createTime) }}</span>
          </div>
        </div>

        <!-- 用户标签 -->
        <div v-if="user.tags && user.tags.length > 0" class="user-tags">
          <h3 class="tags-title">个人标签</h3>
          <div class="tags-container">
            <van-tag
              v-for="tag in user.tags"
              :key="tag"
              type="primary"
              plain
              size="medium"
              class="user-tag"
            >
              {{ tag }}
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="menu-section">
        <van-cell-group inset>
          <van-cell
            title="修改个人信息"
            is-link
            to="/user/update"
            icon="edit"
            class="menu-item"
          />
          <van-cell
            title="我加入的队伍"
            is-link
            to="/user/team/join"
            icon="friends-o"
            class="menu-item"
          />
        </van-cell-group>
      </div>

      <!-- 退出登录按钮 -->
      <div class="logout-section">
        <van-button round block type="danger" @click="logout" class="logout-btn">
          <van-icon name="sign" />
          退出登录
        </van-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import { getCurrentUser } from "../services/user";

const user = ref();
const router = useRouter();

onMounted(async () => {
  user.value = await getCurrentUser();
});

// 跳转编辑页面函数（如果后续需要使用）
const toEdit = (editKey: string, editName: string, currentValue: string) => {
  router.push({
    path: '/user/edit',
    query: {
      editKey,
      editName,
      currentValue,
    }
  });
};

// 退出登录逻辑
const logout = async () => {
  try {
    const res = await myAxios.post('/user/logout');
    if (res.code === 0) {
      Toast.success('已退出登录');
      router.replace('/user/login'); // 跳转到登录页
    } else {
      Toast.fail(res.message || '退出失败');
    }
  } catch (e) {
    Toast.fail('网络异常，退出失败');
  }
};
</script>

<style scoped>
</style>
