<template>
  <div class="user-page">
    <!-- 加载状态 -->
    <van-loading v-if="!user" class="loading-container" size="24px" vertical>
      加载中...
    </van-loading>

    <template v-if="user">
      <!-- 用户信息卡片 -->
      <div class="user-info-card">
        <div class="user-header">
          <div class="user-avatar">
            <img v-if="user.avatarUrl" :src="user.avatarUrl" alt="头像" class="avatar-image" />
            <van-icon v-else name="contact" class="default-avatar" />
          </div>
          <div class="user-basic-info">
            <h2 class="username">{{ user.username || '未设置用户名' }}</h2>
            <p class="user-account">账号：{{ user.userAccount || '未设置' }}</p>
            <p class="user-code">编号：{{ user.planetCode || '未设置' }}</p>
          </div>
        </div>

        <!-- 用户详细信息 -->
        <div class="user-details">
          <div class="detail-item">
            <span class="detail-label">性别</span>
            <span class="detail-value">{{ getGenderText(user.gender) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">邮箱</span>
            <span class="detail-value">{{ user.email || '未设置' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">电话</span>
            <span class="detail-value">{{ user.phone || '未设置' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">注册时间</span>
            <span class="detail-value">{{ formatDate(user.createTime) }}</span>
          </div>
        </div>

        <!-- 用户标签 -->
        <div v-if="user.tags && user.tags.length > 0" class="user-tags">
          <h3 class="tags-title">个人标签</h3>
          <div class="tags-container">
            <van-tag
              v-for="tag in user.tags"
              :key="tag"
              type="primary"
              plain
              size="medium"
              class="user-tag"
            >
              {{ tag }}
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="menu-section">
        <van-cell-group inset>
          <van-cell
            title="修改个人信息"
            is-link
            to="/user/update"
            icon="edit"
            class="menu-item"
          />
          <van-cell
            title="我加入的队伍"
            is-link
            to="/user/team/join"
            icon="friends-o"
            class="menu-item"
          />
        </van-cell-group>
      </div>

      <!-- 退出登录按钮 -->
      <div class="logout-section">
        <van-button round block type="danger" @click="logout" class="logout-btn">
          <van-icon name="sign" />
          退出登录
        </van-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import { getCurrentUser } from "../services/user";

const user = ref();
const router = useRouter();

onMounted(async () => {
  user.value = await getCurrentUser();
  // 处理用户标签
  if (user.value?.tags && typeof user.value.tags === 'string') {
    try {
      user.value.tags = JSON.parse(user.value.tags);
    } catch (e) {
      console.warn('用户标签解析失败:', user.value.tags);
      user.value.tags = [];
    }
  }
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未知';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 获取性别文本
const getGenderText = (gender: number) => {
  switch (gender) {
    case 0:
      return '男';
    case 1:
      return '女';
    default:
      return '未设置';
  }
};

// 跳转编辑页面函数（如果后续需要使用）
const toEdit = (editKey: string, editName: string, currentValue: string) => {
  router.push({
    path: '/user/edit',
    query: {
      editKey,
      editName,
      currentValue,
    }
  });
};

// 退出登录逻辑
const logout = async () => {
  try {
    const res = await myAxios.post('/user/logout');
    if (res.code === 0) {
      Toast.success('已退出登录');
      router.replace('/user/login'); // 跳转到登录页
    } else {
      Toast.fail(res.message || '退出失败');
    }
  } catch (e) {
    Toast.fail('网络异常，退出失败');
  }
};
</script>

<style scoped>
.user-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 20px;
}

.loading-container {
  height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 用户信息卡片 */
.user-info-card {
  background: white;
  margin: 16px;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e8f4fd;
}

.default-avatar {
  width: 80px;
  height: 80px;
  background: #e8f4fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: #1989fa;
}

.user-basic-info {
  flex: 1;
  min-width: 0;
}

.username {
  font-size: 24px;
  font-weight: 700;
  color: #323233;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.user-account {
  font-size: 14px;
  color: #646566;
  margin: 4px 0;
  line-height: 1.4;
}

.user-code {
  font-size: 14px;
  color: #646566;
  margin: 4px 0;
  line-height: 1.4;
}

/* 用户详细信息 */
.user-details {
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f7f8fa;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  width: 80px;
  font-size: 14px;
  color: #646566;
  font-weight: 500;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  font-size: 14px;
  color: #323233;
  line-height: 1.5;
  word-break: break-all;
}

/* 用户标签 */
.user-tags {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.tags-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.user-tag {
  border-radius: 16px;
  font-size: 12px;
}

/* 功能菜单 */
.menu-section {
  margin: 16px;
}

.menu-item {
  font-size: 16px;
}

:deep(.menu-item .van-cell__title) {
  font-weight: 500;
}

:deep(.menu-item .van-icon) {
  color: #1989fa;
  margin-right: 8px;
}

/* 退出登录按钮 */
.logout-section {
  margin: 24px 16px 16px 16px;
}

.logout-btn {
  background: #ee0a24;
  border-color: #ee0a24;
  font-size: 16px;
  font-weight: 600;
  height: 48px;
}

.logout-btn .van-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info-card {
    margin: 12px;
    padding: 20px;
  }

  .user-header {
    gap: 16px;
  }

  .avatar-image,
  .default-avatar {
    width: 70px;
    height: 70px;
  }

  .default-avatar {
    font-size: 32px;
  }

  .username {
    font-size: 20px;
  }

  .detail-label {
    width: 70px;
    font-size: 13px;
  }

  .detail-value {
    font-size: 13px;
  }

  .menu-section {
    margin: 12px;
  }

  .logout-section {
    margin: 20px 12px 12px 12px;
  }
}
</style>
