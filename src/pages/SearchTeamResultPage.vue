<template>
  <div id="searchTeamResultPage">
    <!-- 头部导航 -->
    <van-nav-bar
        title="搜索结果"
        left-text="返回"
        left-arrow
        @click-left="goBack"
        class="custom-nav-bar"
    />
      <!-- 搜索信息 -->
      <div class="search-info">
        <div class="search-keyword">
          <van-icon name="search" />
          <span>{{ searchText || '全部队伍' }}</span>
        </div>
        <div class="search-filter">
          <van-tag :type="getStatusTagType(status)" size="small">
            {{ getStatusText(status) }}
          </van-tag>
        </div>
      </div>

      <!-- 结果统计 -->
      <div class="result-stats">
        <span class="result-count">找到 {{ total }} 个队伍</span>
        <span class="result-time">{{ formatSearchTime() }}</span>
      </div>

    <!-- 搜索结果内容 -->
    <div class="search-content">
      <!-- 加载状态 -->
      <van-loading v-if="loading" class="loading-container" size="24px" vertical>
        搜索中...
      </van-loading>

      <!-- 搜索结果列表 -->
      <div v-else-if="teamList.length > 0" class="result-list">
        <TeamCardList1 :team-list="teamList" />
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <van-empty class="custom-empty">
          <template #image>
            <van-icon name="search" class="empty-icon" />
          </template>
          <template #description>
            <div class="empty-description">
              <h3>{{ getEmptyTitle() }}</h3>
              <p>{{ getEmptyDescription() }}</p>
              <van-button
                type="primary"
                round
                size="small"
                @click="goToTeamPage"
                class="empty-action-btn"
              >
                <van-icon name="arrow-left" />
                返回队伍列表
              </van-button>
            </div>
          </template>
        </van-empty>
      </div>
    </div>

    <!-- 重新搜索按钮 -->
    <div class="search-actions">
      <van-button
        type="primary"
        round
        block
        @click="showSearchDialog = true"
        class="new-search-btn"
      >
        <van-icon name="search" />
        重新搜索
      </van-button>
    </div>

      <!-- 搜索对话框 -->
      <van-dialog
        v-model:show="showSearchDialog"
        title="搜索队伍"
        show-cancel-button
        @confirm="performNewSearch"
        class="search-dialog"
      >
        <div class="search-form">
          <van-field
            v-model="newSearchText"
            label="关键词"
            placeholder="请输入队伍名称或描述"
            clearable
          />
          <van-field name="radio" label="队伍类型">
            <template #input>
              <van-radio-group v-model="newStatus" direction="horizontal">
                <van-radio :name="0">公开</van-radio>
                <van-radio :name="1">加密</van-radio>
                <van-radio :name="-1">全部</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </div>
      </van-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {Toast} from 'vant';
import TeamCardList1 from '../components/TeamCardList-1.vue';
import myAxios from '../plugins/myAxios';
import {getCurrentUser} from '../services/user';

// 类型定义
interface TeamType {
  id: number;
  name: string;
  description: string;
  maxNum: number;
  expireTime: string;
  userId: number;
  status: number;
  createTime: string;
  updateTime: string;
  isDelete: number;
  hasJoinNum?: number;
  hasJoinUsersid?: number[];
  CreateUser?: any;
}

const route = useRoute();
const router = useRouter();

// 搜索参数
const searchText = ref('');
const status = ref(0);
const searchUserId = ref<number | null>(null);
const searchType = ref<'public' | 'encrypted' | 'my'>('public');

// 搜索结果
const teamList = ref<TeamType[]>([]);
const total = ref(0);
const loading = ref(false);
const searchTime = ref(new Date());

// 新搜索对话框
const showSearchDialog = ref(false);
const newSearchText = ref('');
const newStatus = ref(0);

// 页面初始化
onMounted(() => {
  // 从路由参数获取搜索条件
  searchText.value = (route.query.keyword as string) || '';
  status.value = parseInt((route.query.status as string) || '0');
  searchUserId.value = route.query.userId ? parseInt(route.query.userId as string) : null;
  searchType.value = (route.query.type as string) || 'public';

  // 初始化新搜索表单
  newSearchText.value = searchText.value;
  newStatus.value = status.value;

  // 执行搜索
  performSearch();
});

// 执行搜索
const performSearch = async () => {
  loading.value = true;
  searchTime.value = new Date();
  
  try {
    let teamQuery;
    
    if (searchType.value === 'my') {
      // 搜索我创建的队伍
      teamQuery = {
        searchText: searchText.value,
        userId: searchUserId.value
      };
    } else {
      // 搜索公开/加密队伍
      teamQuery = {
        searchText: searchText.value,
        status: status.value
      };
    }
    
    console.log('执行搜索，TeamQuery:', teamQuery);
    const res = await myAxios.post('/team/list', teamQuery);
    console.log('搜索结果:', res);
    
    if (res?.code === 0 && res.data) {
      teamList.value = res.data || [];
      total.value = res.data?.length || 0;
      console.log(`搜索完成，找到 ${total.value} 个队伍`);
    } else {
      Toast.fail('搜索失败，请重试');
      teamList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('搜索失败:', error);
    Toast.fail('搜索失败，请检查网络连接');
    teamList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 执行新搜索
const performNewSearch = () => {
  // 更新搜索参数
  searchText.value = newSearchText.value;
  status.value = newStatus.value;

  // 更新路由参数
  router.replace({
    query: {
      ...route.query,
      keyword: searchText.value,
      status: status.value
    }
  });

  // 执行搜索
  performSearch();
  showSearchDialog.value = false;
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 跳转到队伍页面
const goToTeamPage = () => {
  router.push('/team');
};

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 0:
      return 'success';
    case 1:
      return 'warning';
    case -1:
      return 'default';
    default:
      return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: number) => {
  if (searchType.value === 'my') {
    return '我创建的';
  }

  switch (status) {
    case 0:
      return '公开队伍';
    case 1:
      return '加密队伍';
    case -1:
      return '全部队伍';
    default:
      return '全部队伍';
  }
};

// 格式化搜索时间
const formatSearchTime = () => {
  const now = new Date();
  const diff = now.getTime() - searchTime.value.getTime();
  const seconds = Math.floor(diff / 1000);
  
  if (seconds < 60) {
    return `${seconds} 秒前`;
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)} 分钟前`;
  } else {
    return searchTime.value.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

// 获取空状态标题
const getEmptyTitle = () => {
  if (searchText.value) {
    return '未找到相关队伍';
  }

  if (searchType.value === 'my') {
    return '您还没有创建队伍';
  }

  return '暂无队伍';
};

// 获取空状态描述
const getEmptyDescription = () => {
  if (searchText.value) {
    return `没有找到包含"${searchText.value}"的队伍，试试其他关键词吧`;
  }

  if (searchType.value === 'my') {
    return '创建您的第一个队伍，开始组建团队吧！';
  }

  return '暂时没有符合条件的队伍';
};
</script>

<style scoped>
#searchTeamResultPage {
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

.custom-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

:deep(.custom-nav-bar .van-nav-bar__title) {
  color: white;
  font-weight: 600;
}

:deep(.custom-nav-bar .van-nav-bar__text) {
  color: white;
}

:deep(.custom-nav-bar .van-icon) {
  color: white;
}

.search-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;
  background: white;
  margin-bottom: 8px;
  margin-top: 46px; /* 导航栏高度 */
}

.search-keyword {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.search-keyword .van-icon {
  color: #1989fa;
}

.result-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  font-size: 12px;
  color: #969799;
  border-bottom: 1px solid #ebedf0;
  margin-bottom: 16px;
}

.result-count {
  font-weight: 500;
  color: #323233;
}

/* 搜索内容 */
.search-content {
  padding: 16px 0;
  padding-bottom: 100px; /* 为底部按钮留出更多空间 */
  min-height: calc(100vh - 200px);
}

.loading-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-list {
  padding: 0 16px;
}

/* 空状态 */
.empty-state {
  padding: 40px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.custom-empty {
  background: transparent;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 80px;
  color: #c8c9cc;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-description {
  text-align: center;
  padding: 0;
  width: 100%;
  max-width: 320px;
}

.empty-description h3 {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
}

.empty-description p {
  font-size: 14px;
  color: #969799;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.empty-action-btn {
  background: #1989fa;
  border-color: #1989fa;
}

/* 搜索操作 */
.search-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
}

.new-search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.new-search-btn .van-icon {
  margin-right: 8px;
}

/* 搜索对话框 */
.search-form {
  padding: 16px 0;
}

.search-form .van-field {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-info {
    padding: 10px 12px;
  }
  
  .result-stats {
    padding: 6px 12px;
  }
  
  .search-content {
    padding: 12px 0;
  }
  
  .result-list {
    padding: 0 12px;
  }
  
  .empty-state {
    padding: 30px 16px;
    min-height: 250px;
  }
  
  .empty-icon {
    font-size: 70px;
  }
  
  .search-actions {
    padding: 12px;
  }
  
  .new-search-btn {
    height: 44px;
    font-size: 15px;
  }
}
</style>
