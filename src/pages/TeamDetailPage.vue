<template>
  <div class="team-detail-page">
    <!-- 导航栏 -->
    <van-nav-bar
        title="队伍详情"
        left-text="返回"
        left-arrow
        @click-left="goBack"
        class="detail-nav"
    />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" size="24px" vertical>
      加载中...
    </van-loading>

    <div v-else-if="teamDetail.id">
      <!-- 上半部分：队伍信息 -->
      <div class="team-info-section">
        <div class="background-overlay"></div>
        <div class="team-header">
          <div class="team-avatar">
            <img :src="ikun" alt="队伍头像" class="avatar-image" />
            <div class="member-badge">
              {{ teamDetail.hasJoinNum }}/{{ teamDetail.maxNum }}
            </div>
          </div>
          <div class="team-basic-info">
            <h1 class="team-title">
              {{ teamDetail.name }}
              <van-tag
                  v-if="teamDetail.userId === currentUser?.id"
                  type="success"
                  size="small"
                  class="owner-badge"
              >
                <van-icon name="crown-o" />
                队长
              </van-tag>
            </h1>
            <p class="team-description">{{ teamDetail.description || '暂无描述' }}</p>
            <div class="team-meta">
              <div class="meta-item">
                <van-icon name="calendar-o" />
                <span>创建时间: {{ formatDate(teamDetail.createTime) }}</span>
              </div>
              <div class="meta-item" v-if="teamDetail.expireTime">
                <van-icon name="clock-o" />
                <span>过期时间: {{ formatDate(teamDetail.expireTime) }}</span>
              </div>
              <div class="meta-item">
                <van-tag :type="teamDetail.status === 0 ? 'success' : 'warning'" size="medium">
                  {{ teamStatusEnum[teamDetail.status] }}
                </van-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <!-- 非队长且未加入 -->
          <van-button
              v-if="teamDetail.userId !== currentUser?.id && !teamDetail.hasJoin"
              type="primary"
              size="large"
              round
              block
              class="join-btn"
              @click="preJoinTeam"
          >
            <van-icon name="plus" />
            加入队伍
          </van-button>

          <!-- 队长操作 -->
          <div v-if="teamDetail.userId === currentUser?.id" class="owner-actions">
            <van-button
                type="primary"
                size="large"
                plain
                round
                class="action-btn"
                @click="doUpdateTeam"
            >
              <van-icon name="edit" />
              编辑队伍
            </van-button>
            <van-button
                type="danger"
                size="large"
                plain
                round
                class="action-btn"
                @click="doDeleteTeam"
            >
              <van-icon name="delete-o" />
              解散队伍
            </van-button>
          </div>

          <!-- 已加入队伍 -->
          <van-button
              v-if="teamDetail.userId !== currentUser?.id && teamDetail.hasJoin"
              type="warning"
              size="large"
              plain
              round
              block
              class="quit-btn"
              @click="doQuitTeam"
          >
            <van-icon name="arrow-left" />
            退出队伍
          </van-button>
        </div>
      </div>

      <!-- 下半部分：队伍成员 -->
      <div class="members-section">
        <div class="section-header">
          <h3>队伍成员 ({{ joinedUsers.length }})</h3>
          <van-loading v-if="loadingMembers" size="16px" />
        </div>

        <div class="members-grid">
          <div
              v-for="user in joinedUsers"
              :key="user.id"
              class="member-card"
          >
            <div class="member-avatar">
              <img v-if="user.avatarUrl" :src="user.avatarUrl" alt="" class="avatar-img" />
              <van-icon v-else name="contact" class="default-avatar" />
              <div v-if="user.id === teamDetail.userId" class="leader-crown">
                <van-icon name="crown-o" />
              </div>
            </div>
            <div class="member-info">
              <div class="member-name">{{ user.username }}</div>
              <div class="member-role">
                {{ user.id === teamDetail.userId ? '队长' : '队员' }}
              </div>
              <div class="member-tags" v-if="user.tags && user.tags.length">
                <van-tag
                    v-for="tag in user.tags.slice(0, 2)"
                    :key="tag"
                    size="mini"
                    type="primary"
                    plain
                    class="member-tag"
                >
                  {{ tag }}
                </van-tag>
              </div>
            </div>
          </div>

          <!-- 空位显示 -->
          <div
              v-for="n in Math.max(0, teamDetail.maxNum - joinedUsers.length)"
              :key="'empty-' + n"
              class="member-card empty-slot"
          >
            <div class="member-avatar">
              <van-icon name="plus" class="empty-icon" />
            </div>
            <div class="member-info">
              <div class="member-name">待加入</div>
              <div class="member-role">虚位以待</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 队伍不存在或加载失败 -->
    <van-empty v-else description="队伍信息加载失败" />

    <!-- 密码对话框 -->
    <van-dialog
        v-model:show="showPasswordDialog"
        title="请输入队伍密码"
        :show-cancel-button="true"
        confirm-button-text="加入"
        cancel-button-text="取消"
        class="password-dialog"
        @confirm="doJoinTeam"
        @cancel="doJoinCancel"
    >
      <div class="password-input-container">
        <van-field
            v-model="password"
            type="password"
            placeholder="请输入队伍密码"
            label="密码"
            label-width="60px"
            :border="false"
            class="password-field"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { TeamType } from "../models/team";
import { UserType } from "../models/user";
import { teamStatusEnum } from "../constants/team";
import ikun from '../assets/ikun.png';
import myAxios from "../plugins/myAxios";
import { Dialog, Toast } from "vant";
import { onMounted, ref } from "vue";
import { getCurrentUser } from "../services/user";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();

const loading = ref(true);
const teamDetail = ref<TeamType>({} as TeamType);
const joinedUsers = ref<UserType[]>([]);
const loadingMembers = ref(false);
const currentUser = ref();
const showPasswordDialog = ref(false);
const password = ref('');

onMounted(async () => {
  currentUser.value = await getCurrentUser();
  await loadTeamDetail();
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 加载队伍详情 - 修改为从列表接口获取
const loadTeamDetail = async () => {
  const teamId = route.query.id;
  if (!teamId) {
    Toast.fail('队伍ID不存在');
    goBack();
    return;
  }

  try {
    loading.value = true;

    // 从队伍列表中查找指定的队伍
    const listRes = await myAxios.get('/team/list', {
      params: {
        pageNum: 1,
        pageSize: 100, // 设置较大的页面大小以确保能找到目标队伍
        searchText: '', // 可以根据需要添加搜索条件
      }
    });

    if (listRes?.code === 0 && listRes.data) {
      // 在列表中查找目标队伍
      const targetTeam = listRes.data.find((team: TeamType) => team.id == teamId);

      if (targetTeam) {
        teamDetail.value = targetTeam;
        // 加载队伍成员 - 注意这里使用的是 hasJoinUsersid (小写)
        await loadTeamMembers(targetTeam.hasJoinUsersid || []);
      } else {
        Toast.fail('队伍不存在');
        goBack();
      }
    } else {
      Toast.fail('获取队伍列表失败');
      goBack();
    }
  } catch (error) {
    console.error('加载队伍详情失败:', error);
    Toast.fail('加载失败，请检查网络连接');
    goBack();
  } finally {
    loading.value = false;
  }
};

// 加载队伍成员
const loadTeamMembers = async (userIds: number[]) => {
  if (!userIds || userIds.length === 0) {
    joinedUsers.value = [];
    return;
  }

  loadingMembers.value = true;
  try {
    // 这里需要确认后端接口的正确调用方式
    // 可能需要调整参数名或请求方式
    const res = await myAxios.get('/user/search/ids', {
      params: {
        userIds: userIds.join(',') // 尝试用逗号分隔的字符串
      }
    });

    // 如果上面的方式不行，可以尝试以下方式：
    // const res = await myAxios.post('/user/search/ids', { userIds });

    if (res?.code === 0) {
      joinedUsers.value = res.data || [];
    } else {
      console.error('获取成员信息失败:', res);
      // 如果无法获取详细的成员信息，至少显示基本信息
      joinedUsers.value = userIds.map(id => ({
        id,
        username: `用户${id}`,
        avatarUrl: '',
        tags: []
      }));
    }
  } catch (error) {
    console.error('加载成员失败:', error);
    // 容错处理：显示基本的成员占位信息
    joinedUsers.value = userIds.map(id => ({
      id,
      username: `用户${id}`,
      avatarUrl: '',
      tags: []
    }));
  } finally {
    loadingMembers.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 准备加入队伍
const preJoinTeam = () => {
  if (teamDetail.value.status === 0) {
    doJoinTeam();
  } else {
    showPasswordDialog.value = true;
  }
};

const doJoinCancel = () => {
  password.value = '';
};

/**
 * 加入队伍
 */
const doJoinTeam = async () => {
  const res = await myAxios.post('/team/join', {
    teamId: teamDetail.value.id,
    password: password.value
  });
  if (res?.code === 0) {
    Toast.success('加入成功');
    doJoinCancel();
    showPasswordDialog.value = false;
    // 重新加载队伍信息
    await loadTeamDetail();
  } else {
    Toast.fail('加入失败' + (res.description ? `，${res.description}` : ''));
  }
};

/**
 * 跳转至更新队伍页
 */
const doUpdateTeam = () => {
  router.push({
    path: '/team/update',
    query: {
      id: teamDetail.value.id,
    }
  });
};

/**
 * 退出队伍
 */
const doQuitTeam = async () => {
  const res = await myAxios.post('/team/quit', {
    teamId: teamDetail.value.id
  });
  if (res?.code === 0) {
    Toast.success('退出成功');
    goBack();
  } else {
    Toast.fail('操作失败' + (res.description ? `，${res.description}` : ''));
  }
};

/**
 * 解散队伍
 */
const doDeleteTeam = async () => {
  await Dialog.confirm({
    title: '确认解散',
    message: '解散后队伍将永久删除，确定要解散这个队伍吗？',
  });

  const res = await myAxios.post('/team/delete', {
    id: teamDetail.value.id,
  });
  if (res?.code === 0) {
    Toast.success('解散成功');
    goBack();
  } else {
    Toast.fail('操作失败' + (res.description ? `，${res.description}` : ''));
  }
};
</script>

<style scoped>
.team-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.detail-nav {
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  z-index: 100;
}

.loading-container {
  height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.team-info-section {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 20px;
  min-height: 45vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.15"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.15"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
  opacity: 0.4;
}

.team-header {
  position: relative;
  z-index: 2;
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.team-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 85px;
  height: 85px;
  border-radius: 20px;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.member-badge {
  position: absolute;
  bottom: -10px;
  right: -10px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: 700;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.team-basic-info {
  flex: 1;
  min-width: 0;
}

.team-title {
  font-size: 26px;
  font-weight: 800;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  line-height: 1.2;
}

.owner-badge {
  font-size: 11px;
  background: rgba(255, 215, 0, 0.9);
  color: #1a1a1a;
  border: 1px solid rgba(255, 215, 0, 0.3);
  font-weight: 600;
}

.team-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 24px 0;
  opacity: 0.95;
  font-weight: 400;
}

.team-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
}

.action-buttons {
  position: relative;
  z-index: 2;
  margin-top: 28px;
}

.owner-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.join-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.4);
  color: white;
  font-weight: 700;
  backdrop-filter: blur(10px);
}

.quit-btn {
  background: rgba(239, 68, 68, 0.2);
  border: 2px solid rgba(239, 68, 68, 0.4);
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.members-section {
  padding: 24px 20px;
  background: white;
  min-height: 55vh;
  border-radius: 24px 24px 0 0;
  margin-top: -12px;
  position: relative;
  z-index: 3;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
}

.member-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  background: #ffffff;
  border-radius: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  position: relative;
}

.member-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.empty-slot {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  opacity: 0.7;
}

.empty-slot:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.member-avatar {
  position: relative;
  margin-bottom: 12px;
}

.avatar-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e2e8f0;
}

.default-avatar {
  width: 60px;
  height: 60px;
  background: #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #94a3b8;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #cbd5e1;
}

.leader-crown {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.member-info {
  text-align: center;
  width: 100%;
}

.member-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  word-break: break-all;
}

.member-role {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.member-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.member-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 密码对话框样式 */
.password-input-container {
  padding: 20px 0;
}

.password-field {
  background: #f8fafc;
  border-radius: 12px;
}

:deep(.password-dialog .van-dialog__content) {
  padding: 0 24px;
}

:deep(.password-field .van-field__control) {
  background: transparent;
}

:deep(.password-field .van-field__body) {
  background: #f8fafc;
  border-radius: 12px;
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .members-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .team-header {
    gap: 16px;
  }

  .avatar-image {
    width: 70px;
    height: 70px;
  }

  .team-title {
    font-size: 22px;
  }

  .owner-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}
</style>