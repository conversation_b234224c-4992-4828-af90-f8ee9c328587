
<template>
  <div class="team-detail-page">
    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" size="24px" vertical>
      加载中...
    </van-loading>

    <div v-else-if="teamDetail.id">
      <!-- 上半部分：队伍信息 -->
      <div class="team-info-section">
        <div class="background-overlay"></div>
        <div class="team-header">
          <div class="team-avatar">
            <img :src="teamAvatar" alt="队伍头像" class="avatar-image" />
            <div class="member-badge">
              {{ teamDetail.hasJoinNum }}/{{ teamDetail.maxNum }}
            </div>
          </div>
          <div class="team-basic-info">
            <h1 class="team-title">
              {{ teamDetail.name }}
              <van-tag
                  v-if="teamDetail.userId === currentUser?.id"
                  type="success"
                  size="small"
                  class="owner-badge"
              >
                <van-icon name="crown-o" />
                队长
              </van-tag>
            </h1>
            <p class="team-description">{{ teamDetail.description || '暂无描述' }}</p>
            <div class="team-meta">
              <div class="meta-item">
                <van-icon name="calendar-o" />
                <span>创建时间: {{ formatDate(teamDetail.createTime) }}</span>
              </div>
              <div class="meta-item" v-if="teamDetail.expireTime">
                <van-icon name="clock-o" />
                <span>过期时间: {{ formatDate(teamDetail.expireTime) }}</span>
              </div>
              <div class="meta-item">
                <van-tag :type="getStatusTagType(teamDetail.status)" size="medium">
                  <van-icon :name="getStatusIcon(teamDetail.status)" />
                  {{ teamStatusEnum[teamDetail.status] }}
                </van-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <!-- 非队长且未加入 -->
          <van-button
              v-if="teamDetail.userId !== currentUser?.id && !teamDetail.hasJoin"
              type="primary"
              size="large"
              round
              block
              class="join-btn"
              @click="preJoinTeam"
          >
            <van-icon name="plus" />
            加入队伍
          </van-button>

          <!-- 队长操作 -->
          <div v-if="teamDetail.userId === currentUser?.id" class="owner-actions">
            <van-button
                type="primary"
                size="large"
                round
                class="join-btn"
                @click="doUpdateTeam"
            >
              <van-icon name="edit" />
              编辑队伍
            </van-button>
            <van-button
                type="primary"
                size="large"
                round
                class="join-btn"
                @click="doDeleteTeam"
            >
              <van-icon name="delete-o" />
              解散队伍
            </van-button>
          </div>

          <!-- 已加入队伍 -->
          <van-button
              v-if="teamDetail.userId !== currentUser?.id && teamDetail.hasJoin"
              type="primary"
              size="large"
              round
              block
              class="join-btn"
              @click="doQuitTeam"
          >
            <van-icon name="arrow-left" />
            退出队伍
          </van-button>
        </div>
      </div>

      <!-- 下半部分：队伍成员 -->
      <div class="members-section">
        <div class="section-header">
          <h3>队伍成员 ({{ joinedUsers.length }})</h3>
          <van-loading v-if="loadingMembers" size="16px" />
        </div>

        <!-- 使用 UserCardList 组件展示成员 -->
        <user-card-list :user-list="joinedUsers" :loading="loadingMembers" />

        <!-- 空位显示 -->
        <div v-if="teamDetail.maxNum > joinedUsers.length" class="empty-slots">
          <div class="empty-slots-header">
            <h4>空余位置 ({{ teamDetail.maxNum - joinedUsers.length }})</h4>
          </div>
          <div class="empty-slots-grid">
            <div
                v-for="n in Math.max(0, teamDetail.maxNum - joinedUsers.length)"
                :key="'empty-' + n"
                class="empty-slot-card"
            >
              <div class="empty-slot-avatar">
                <van-icon name="plus" class="empty-icon" />
              </div>
              <div class="empty-slot-info">
                <div class="empty-slot-name">待加入</div>
                <div class="empty-slot-role">虚位以待</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 队伍不存在或加载失败 -->
    <van-empty v-else description="队伍信息加载失败" />

    <!-- 密码对话框 -->
    <van-dialog
        v-model:show="showPasswordDialog"
        title="请输入队伍密码"
        :show-cancel-button="true"
        confirm-button-text="加入"
        cancel-button-text="取消"
        class="password-dialog"
        @confirm="doJoinTeam"
        @cancel="doJoinCancel"
    >
      <div class="password-input-container">
        <van-field
            v-model="password"
            type="password"
            placeholder="请输入队伍密码"
            label="密码"
            label-width="60px"
            :border="false"
            class="password-field"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { TeamType } from "../models/team";
import { UserType } from "../models/user";
import { teamStatusEnum } from "../constants/team";
import teamAvatar from '../assets/OIP.jpg';
import myAxios from "../plugins/myAxios";
import { Dialog, Toast } from "vant";
import { onMounted, ref } from "vue";
import { getCurrentUser } from "../services/user";
import { useRouter, useRoute } from "vue-router";
import UserCardList from "../components/UserCardList.vue";

const router = useRouter();
const route = useRoute();

const loading = ref(true);
const teamDetail = ref<TeamType>({} as TeamType);
const joinedUsers = ref<UserType[]>([]);
const loadingMembers = ref(false);
const currentUser = ref();
const showPasswordDialog = ref(false);
const password = ref('');

onMounted(async () => {
  currentUser.value = await getCurrentUser();
  await loadTeamDetail();
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 加载队伍详情 - 修改为从列表接口获取
const loadTeamDetail = async () => {
  const teamId = route.query.id;
  if (!teamId) {
    Toast.fail('队伍ID不存在');
    router.back();
    return;
  }

  try {
    loading.value = true;

    // 从队伍列表中查找指定的队伍
    const listRes = await myAxios.get('/team/list', {
      params: {
        pageNum: 1,
        pageSize: 100, // 设置较大的页面大小以确保能找到目标队伍
        searchText: '', // 可以根据需要添加搜索条件
      }
    });

    if (listRes?.code === 0 && listRes.data) {
      // 在列表中查找目标队伍
      const targetTeam = listRes.data.find((team: TeamType) => team.id == teamId);

      if (targetTeam) {
        teamDetail.value = targetTeam;
        // 加载队伍成员 - 注意这里使用的是 hasJoinUsersid (小写)
        await loadTeamMembers(targetTeam.hasJoinUsersid || []);
      } else {
        Toast.fail('队伍不存在');
        router.back();
      }
    } else {
      Toast.fail('获取队伍列表失败');
      router.back();
    }
  } catch (error) {
    console.error('加载队伍详情失败:', error);
    Toast.fail('加载失败，请检查网络连接');
    router.back();
  } finally {
    loading.value = false;
  }
};

// 加载队伍成员
const loadTeamMembers = async (userIds: number[]) => {
  if (!userIds || userIds.length === 0) {
    joinedUsers.value = [];
    return;
  }

  loadingMembers.value = true;
  try {
    // 根据后端接口 @GetMapping("/search/{ids}") 调用
    // 参数名为 ids，类型为 List<Long>，通过 @RequestParam 传递
    // 使用 URLSearchParams 来正确构建查询参数
    const params = new URLSearchParams();
    userIds.forEach(id => params.append('ids', id.toString()));

    const res = await myAxios.get('/user/search/ids?' + params.toString());

    if (res?.code === 0 && res.data) {
      // 处理返回的用户数据
      const users = res.data.map((user: any) => {
        // 确保 tags 字段正确处理
        if (user.tags && typeof user.tags === 'string') {
          try {
            user.tags = JSON.parse(user.tags);
          } catch (e) {
            console.warn('用户标签解析失败:', user.tags);
            user.tags = [];
          }
        } else if (!user.tags) {
          user.tags = [];
        }
        return user;
      });

      joinedUsers.value = users;
      console.log('成功加载队伍成员:', users);
    } else {
      console.error('获取成员信息失败:', res);
      Toast.fail('获取成员信息失败');
      // 如果无法获取详细的成员信息，至少显示基本信息
      joinedUsers.value = userIds.map(id => ({
        id,
        username: `用户${id}`,
        userAccount: `user${id}`,
        avatarUrl: '',
        profile: '暂无简介',
        gender: 0,
        userStatus: 0,
        userRole: 0,
        planetCode: '',
        tags: [],
        createTime: new Date()
      }));
    }
  } catch (error) {
    console.error('加载成员失败:', error);
    Toast.fail('加载成员失败，请检查网络连接');
    // 容错处理：显示基本的成员占位信息
    joinedUsers.value = userIds.map(id => ({
      id,
      username: `用户${id}`,
      userAccount: `user${id}`,
      avatarUrl: '',
      profile: '暂无简介',
      gender: 0,
      userStatus: 0,
      userRole: 0,
      planetCode: '',
      tags: [],
      createTime: new Date()
    }));
  } finally {
    loadingMembers.value = false;
  }
};

// 注意：返回功能由 BasicLayout 处理，不需要自定义 goBack 函数

// 准备加入队伍
const preJoinTeam = () => {
  if (teamDetail.value.status === 0) {
    doJoinTeam();
  } else {
    showPasswordDialog.value = true;
  }
};

const doJoinCancel = () => {
  password.value = '';
};

/**
 * 加入队伍
 */
const doJoinTeam = async () => {
  const res = await myAxios.post('/team/join', {
    teamId: teamDetail.value.id,
    password: password.value
  });
  if (res?.code === 0) {
    Toast.success('加入成功');
    doJoinCancel();
    showPasswordDialog.value = false;
    // 重新加载队伍信息
    await loadTeamDetail();
  } else {
    Toast.fail('加入失败' + (res.description ? `，${res.description}` : ''));
  }
};

/**
 * 跳转至更新队伍页
 */
const doUpdateTeam = () => {
  router.push({
    path: '/team/update',
    query: {
      id: teamDetail.value.id,
    }
  });
};

/**
 * 退出队伍
 */
const doQuitTeam = async () => {
  const res = await myAxios.post('/team/quit', {
    teamId: teamDetail.value.id
  });
  if (res?.code === 0) {
    Toast.success('退出成功');
    router.back();
  } else {
    Toast.fail('操作失败' + (res.description ? `，${res.description}` : ''));
  }
};

/**
 * 解散队伍
 */
const doDeleteTeam = async () => {
  await Dialog.confirm({
    title: '确认解散',
    message: '解散后队伍将永久删除，确定要解散这个队伍吗？',
  });

  const res = await myAxios.post('/team/delete', {
    id: teamDetail.value.id,
  });
  if (res?.code === 0) {
    Toast.success('解散成功');
    router.back();
  } else {
    Toast.fail('操作失败' + (res.description ? `，${res.description}` : ''));
  }
};
</script>

<style scoped>
.team-detail-page {
  min-height: 100vh;
  background: #e3f2fd; /* 浅蓝色背景 */
}

.loading-container {
  height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.team-info-section {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 20px;
  min-height: 45vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.15"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.15"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
  opacity: 0.4;
}

.team-header {
  position: relative;
  z-index: 2;
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.team-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 85px;
  height: 85px;
  border-radius: 20px;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.member-badge {
  position: absolute;
  bottom: -10px;
  right: -10px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: 700;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.team-basic-info {
  flex: 1;
  min-width: 0;
}

.team-title {
  font-size: 26px;
  font-weight: 800;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  line-height: 1.2;
}

.owner-badge {
  font-size: 11px;
  background: rgba(255, 215, 0, 0.9);
  color: #1a1a1a;
  border: 1px solid rgba(255, 215, 0, 0.3);
  font-weight: 600;
}

.team-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 24px 0;
  opacity: 0.95;
  font-weight: 400;
}

.team-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
}

.action-buttons {
  position: relative;
  z-index: 2;
  margin-top: 28px;
}

.owner-actions {
  display: flex;
  gap: 12px;
}

.join-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.4);
  color: white;
  font-weight: 700;
  backdrop-filter: blur(10px);
}

.members-section {
  padding: 24px 20px;
  background: white;
  min-height: 55vh;
  border-radius: 24px 24px 0 0;
  margin-top: -12px;
  position: relative;
  z-index: 3;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

/* 空位显示样式 */
.empty-slots {
  margin-top: 24px;
}

.empty-slots-header {
  margin-bottom: 16px;
}

.empty-slots-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  margin: 0;
}

.empty-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
}

.empty-slot-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 16px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.empty-slot-card:hover {
  opacity: 0.9;
  border-color: #94a3b8;
}

.empty-slot-avatar {
  position: relative;
  margin-bottom: 12px;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #cbd5e1;
}

.empty-slot-info {
  text-align: center;
  width: 100%;
}

.empty-slot-name {
  font-size: 14px;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 4px;
}

.empty-slot-role {
  font-size: 12px;
  color: #cbd5e1;
}

/* 密码对话框样式 */
.password-input-container {
  padding: 20px 0;
}

.password-field {
  background: #f8fafc;
  border-radius: 12px;
}

:deep(.password-dialog .van-dialog__content) {
  padding: 0 24px;
}

:deep(.password-field .van-field__control) {
  background: transparent;
}

:deep(.password-field .van-field__body) {
  background: #f8fafc;
  border-radius: 12px;
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .empty-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .team-header {
    gap: 16px;
  }

  .avatar-image {
    width: 70px;
    height: 70px;
  }

  .team-title {
    font-size: 22px;
  }

  .owner-actions {
    flex-direction: column;
  }

  .join-btn {
    width: 100%;
  }
}
</style>