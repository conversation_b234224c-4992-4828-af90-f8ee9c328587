<template>
  <!-- 公告栏/广告位 -->
  <div class="banner-section">
    <!-- 方案1: 轮播图广告 -->
    <van-swipe class="banner-swipe" :autoplay="3000" indicator-color="white">
      <van-swipe-item v-for="(banner, index) in bannerList" :key="index">
        <div class="banner-item" @click="onBannerClick(banner)">
          <img :src="banner.image" :alt="banner.title" class="banner-image" />
          <div class="banner-overlay" v-if="banner.title || banner.description">
            <div class="banner-title" v-if="banner.title">{{ banner.title }}</div>
            <div class="banner-desc" v-if="banner.description">{{ banner.description }}</div>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>

    <!-- 方案2: 公告栏 (可以和轮播图二选一或者都保留) -->
    <van-notice-bar
        v-if="noticeText"
        left-icon="volume-o"
        :text="noticeText"
        mode="closeable"
        background="#fff7e6"
        color="#ed6a0c"
        @click="onNoticeClick"
        @close="onNoticeClose"
    />
  </div>

  <!-- 心动模式开关 -->
  <van-cell center title="心动模式">
    <template #right-icon>
      <van-switch v-model="isMatchMode" size="24" />
    </template>
  </van-cell>

  <!-- 用户列表 -->
  <user-card-list :user-list="userList" :loading="loading"/>
  <van-empty v-if="!loading && (!userList || userList.length < 1)" description="数据为空"/>
</template>

<script setup lang="ts">
import { ref, watchEffect, onMounted } from 'vue';
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import UserCardList from "../components/UserCardList.vue";
import { UserType } from "../models/user";

const isMatchMode = ref<boolean>(false);
const userList = ref<UserType[]>([]);
const loading = ref<boolean>(true);

// 公告栏数据
const noticeText = ref<string>('欢迎来到圈友！在这里遇见志同道合的朋友 🎉');

// 轮播图数据
const bannerList = ref([
  {
    id: 1,
    title: '找到你的圈子',
    description: '志同道合，一拍即合',
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=300&fit=crop',
    link: '/search'
  },
  {
    id: 2,
    title: '心动模式',
    description: '智能匹配，遇见更好的自己',
    image: 'https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=800&h=300&fit=crop',
    link: null
  },
  {
    id: 3,
    title: '加入队伍',
    description: '组队刷副本，一起变更强',
    image: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=800&h=300&fit=crop',
    link: '/team'
  }
]);

/**
 * 加载数据
 */
const loadData = async () => {
  let userListData;
  loading.value = true;

  try {
    // 心动模式，根据标签匹配用户
    if (isMatchMode.value) {
      const num = 10;
      const response = await myAxios.get('/user/match', {
        params: {
          num,
        },
      });
      console.log('/user/match succeed', response);
      userListData = response?.data;
    } else {
      // 普通模式，直接分页查询用户
      const response = await myAxios.get('/user/recommend', {
        params: {
          PageSize: 8,
          PageNum: 1,
        },
      });
      console.log('/user/recommend succeed', response);
      userListData = response?.data?.records;
    }

    if (userListData) {
      userListData.forEach((user: UserType) => {
        // 检查 tags 是否为字符串类型，如果是则解析
        if (user.tags && typeof user.tags === 'string') {
          try {
            user.tags = JSON.parse(user.tags);
          } catch (e) {
            console.warn('tags 解析失败', user.tags);
            user.tags = [];
          }
        }
      });
      userList.value = userListData;
    }
  } catch (error) {
    console.error('请求失败', error);
    Toast.fail('请求失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 加载公告和广告数据
 */
const loadBannerData = async () => {
  try {
    // 这里可以从后端加载公告和轮播图数据
    // const response = await myAxios.get('/system/banner');
    // bannerList.value = response.data;

    // const noticeResponse = await myAxios.get('/system/notice');
    // noticeText.value = noticeResponse.data.content;
  } catch (error) {
    console.warn('加载公告数据失败', error);
  }
};

/**
 * 轮播图点击事件
 */
const onBannerClick = (banner: any) => {
  console.log('点击轮播图', banner);
  if (banner.link) {
    // 可以使用 router.push(banner.link) 进行页面跳转
    Toast.success(`点击了: ${banner.title}`);
  }
};

/**
 * 公告栏点击事件
 */
const onNoticeClick = () => {
  Toast.success('查看公告详情');
  // 可以跳转到公告详情页面
};

/**
 * 公告栏关闭事件
 */
const onNoticeClose = () => {
  noticeText.value = '';
};

watchEffect(() => {
  loadData();
});

onMounted(() => {
  loadBannerData();
});
</script>

<style scoped>
.banner-section {
  margin-bottom: 10px;
}

.banner-swipe {
  width: 100%;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px 16px 16px;
}

.banner-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.banner-desc {
  font-size: 14px;
  opacity: 0.9;
}

/* 如果你只想要单张图片的广告位，可以使用这个样式 */
.simple-banner {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 10px;
  cursor: pointer;
}

.simple-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>