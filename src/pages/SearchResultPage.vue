<template>
  <div class="search-page">
    <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        loading-text="加载中..."
        @load="onLoad"
    >
      <user-card-list :user-list="userList" :loading="false" />
    </van-list>

    <van-empty
        v-if="!loading && userList.length === 0 && finished"
        description="搜索结果为空"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import myAxios from '../plugins/myAxios';
import { Toast } from 'vant';
import qs from 'qs';
import UserCardList from '../components/UserCardList.vue';
import type { UserType } from '../models/user';

const route = useRoute();
const tags = route.query.tags;

const userList = ref<UserType[]>([]);
const loading = ref(false);
const finished = ref(false);
const pageNum = ref(1);
const pageSize = ref(10);

interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

const onLoad = async () => {
  console.log('onLoad 触发，当前状态:', {
    loading: loading.value,
    finished: finished.value,
    pageNum: pageNum.value
  });

  // 如果已经在加载或已经完成，直接返回
  if (loading.value || finished.value) {
    return;
  }

  loading.value = true;

  try {
    console.log('发送请求，页码:', pageNum.value);

    const res = await myAxios.get('/user/search/tags', {
      params: {
        tagNameList: tags,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
      },
      paramsSerializer: (params) => qs.stringify(params, { indices: false }),
    });

    const pageData: PageResponse<UserType> = res.data;
    console.log('接收到数据:', pageData);

    // 处理 tags 字段
    pageData.records.forEach((user) => {
      if (typeof user.tags === 'string') {
        try {
          user.tags = JSON.parse(user.tags);
        } catch {
          user.tags = [];
        }
      }
    });

    // 添加数据到列表
    userList.value.push(...pageData.records);

    // 更新分页状态
    if (pageData.current >= pageData.pages || pageData.records.length === 0) {
      finished.value = true;
      console.log('数据加载完成');
    } else {
      pageNum.value++;
      console.log('准备加载下一页:', pageNum.value);
    }

  } catch (e) {
    console.error('请求出错', e);
    Toast.fail('加载失败，请稍后重试');
  } finally {
    loading.value = false;
    console.log('loading 设为 false');
  }
};

// 初始化加载第一页
onMounted(() => {
  console.log('组件挂载，开始加载数据');
  onLoad();
});
</script>

<style scoped>
.search-page {
  height: 100%;
  min-height: calc(100vh - 96px);
}
</style>