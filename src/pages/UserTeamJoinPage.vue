<template>
  <div id="userTeamJoinPage">
    <van-search v-model="searchText" placeholder="搜索我加入的队伍" @search="onSearch" />

    <!-- 队伍列表 -->
    <div class="team-content">
      <team-card-list-1 v-if="teamList?.length > 0" :teamList="teamList" />

      <!-- 加载状态 -->
      <van-loading v-if="loading" class="loading-container" size="24px" vertical>
        加载中...
      </van-loading>

      <!-- 空状态 -->
      <div v-if="teamList?.length < 1 && !loading" class="empty-state">
        <van-empty
          image="friends-o"
          class="custom-empty"
        >
          <template #description>
            <div class="empty-description">
              <h3>{{ getEmptyTitle() }}</h3>
              <p>{{ getEmptyDescription() }}</p>
              <van-button
                type="primary"
                round
                size="small"
                @click="goToTeamPage"
                class="empty-action-btn"
              >
                <van-icon name="search" />
                去发现队伍
              </van-button>
            </div>
          </template>
        </van-empty>
      </div>

      <!-- 更多提示 -->
      <div v-if="teamList?.length > 0 && currentPage >= totalPages" class="more-tip">
        已显示全部队伍
      </div>

      <!-- 分页组件 -->
      <div v-if="total > 0" class="pagination-container">
        <van-pagination
          v-model="currentPage"
          :total-items="total"
          :items-per-page="pageSize"
          :show-page-size="3"
          @change="onPageChange"
        />
        <div class="pagination-info">
          共 {{ total }} 个队伍，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {useRouter} from "vue-router";
import TeamCardList1 from "../components/TeamCardList-1.vue";
import {onMounted, ref} from "vue";
import myAxios from "../plugins/myAxios";
import {Toast} from "vant";

const router = useRouter();
const searchText = ref('');

const teamList = ref([]);

/**
 * 搜索队伍
 * @param val
 * @returns {Promise<void>}
 */
const listTeam = async (val = '') => {
  const res = await myAxios.get("/team/list/my/join", {
    params: {
      searchText: val,
      pageNum: 1,
    },
  });
  if (res?.code === 0) {
    teamList.value = res.data;
  } else {
    Toast.fail('加载队伍失败，请刷新重试');
  }
}


// 页面加载时只触发一次
onMounted( () => {
  listTeam();
})

const onSearch = (val) => {
  listTeam(val);
};

</script>

<style scoped>
#teamPage {

}
</style>
