<template>
  <div id="userTeamJoinPage">
    <van-search v-model="searchText" placeholder="搜索我加入的队伍" @search="onSearch" />

    <!-- 队伍列表 -->
    <div class="team-content">
      <team-card-list-1 v-if="teamList?.length > 0" :teamList="teamList" />

      <!-- 加载状态 -->
      <van-loading v-if="loading" class="loading-container" size="24px" vertical>
        加载中...
      </van-loading>

      <!-- 空状态 -->
      <div v-if="teamList?.length < 1 && !loading" class="empty-state">
        <van-empty class="custom-empty">
          <template #image>
            <van-icon name="friends-o" class="empty-icon" />
          </template>
          <template #description>
            <div class="empty-description">
              <h3>{{ getEmptyTitle() }}</h3>
              <p>{{ getEmptyDescription() }}</p>
              <van-button
                type="primary"
                round
                size="small"
                @click="goToTeamPage"
                class="empty-action-btn"
              >
                <van-icon name="search" />
                去发现队伍
              </van-button>
            </div>
          </template>
        </van-empty>
      </div>

      <!-- 更多提示 -->
      <div v-if="teamList?.length > 0 && currentPage >= totalPages" class="more-tip">
        已显示全部队伍
      </div>

      <!-- 分页组件 -->
      <div v-if="total > 0" class="pagination-container">
        <van-pagination
          v-model="currentPage"
          :total-items="total"
          :items-per-page="pageSize"
          :show-page-size="3"
          @change="onPageChange"
        />
        <div class="pagination-info">
          共 {{ total }} 个队伍，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {useRouter} from "vue-router";
import TeamCardList1 from "../components/TeamCardList-1.vue";
import {onMounted, ref, computed} from "vue";
import myAxios from "../plugins/myAxios";
import {Toast} from "vant";
import {getCurrentUser} from "../services/user";

const router = useRouter();
const searchText = ref('');
const loading = ref(false);
const currentUser = ref();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(6);
const total = ref(0);
const totalPages = computed(() => Math.ceil(total.value / pageSize.value));

const teamList = ref([]);

/**
 * 搜索我加入的队伍（分页版本）
 * @param val 搜索关键词
 * @returns {Promise<void>}
 */
const listTeam = async (val = '') => {
  loading.value = true;
  try {
    const res = await myAxios.get("/team/list/my/join", {
      params: {
        searchText: val,
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      },
    });
    if (res?.code === 0 && res.data) {
      // 如果返回的是分页数据
      if (res.data.records) {
        teamList.value = res.data.records || [];
        total.value = res.data.total || 0;
      } else {
        // 如果返回的是数组数据
        teamList.value = res.data || [];
        total.value = res.data?.length || 0;
      }
    } else {
      Toast.fail('加载队伍失败，请刷新重试');
      teamList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('加载我加入的队伍失败:', error);
    Toast.fail('加载队伍失败，请检查网络连接');
    teamList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

/**
 * 分页改变事件
 * @param page 页码
 */
const onPageChange = (page) => {
  currentPage.value = page;
  listTeam(searchText.value);
}

// 页面加载时只触发一次
onMounted(async () => {
  currentUser.value = await getCurrentUser();
  listTeam();
})

const onSearch = (val) => {
  currentPage.value = 1; // 搜索时重置页码
  listTeam(val);
};

// 获取空状态标题
const getEmptyTitle = () => {
  if (searchText.value) {
    return '未找到相关队伍';
  }
  return '您还没有加入任何队伍';
};

// 获取空状态描述
const getEmptyDescription = () => {
  if (searchText.value) {
    return `没有找到包含"${searchText.value}"的队伍，试试其他关键词吧`;
  }
  return '快去发现并加入感兴趣的队伍吧！';
};

// 跳转到队伍页面
const goToTeamPage = () => {
  router.push('/team');
};

</script>

<style scoped>
#userTeamJoinPage {
  position: relative;
  padding-bottom: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.team-content {
  min-height: auto;
}

.loading-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分页容器 */
.pagination-container {
  margin: 24px 0;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.pagination-info {
  text-align: center;
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.custom-empty {
  background: transparent;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 80px;
  color: #c8c9cc;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.empty-description {
  text-align: center;
  padding: 0;
  width: 100%;
  max-width: 320px;
}

.empty-description h3 {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.empty-description p {
  font-size: 14px;
  color: #969799;
  margin: 0 0 24px 0;
  line-height: 1.6;
  max-width: 280px;
  margin-left: auto;
  margin-right: auto;
}

.empty-action-btn {
  background: #1989fa;
  border-color: #1989fa;
  font-size: 14px;
  padding: 8px 20px;
  min-width: 120px;
}

.empty-action-btn .van-icon {
  margin-right: 4px;
  font-size: 16px;
}

/* 更多提示 */
.more-tip {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

/* 分页组件样式优化 */
:deep(.van-pagination) {
  justify-content: center;
}

:deep(.van-pagination__item) {
  border-radius: 8px;
  margin: 0 4px;
}

:deep(.van-pagination__item--active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

/* 搜索框样式优化 */
:deep(.van-search) {
  background: #f8f9fa;
  padding: 12px 16px;
}

:deep(.van-search__content) {
  border-radius: 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    margin: 16px 0;
    padding: 12px;
  }

  .pagination-info {
    font-size: 12px;
  }

  .empty-state {
    padding: 30px 16px;
    min-height: 250px;
  }

  .empty-icon {
    font-size: 70px;
  }

  .empty-description h3 {
    font-size: 16px;
  }

  .empty-description p {
    font-size: 13px;
    max-width: 240px;
  }

  .empty-action-btn {
    font-size: 13px;
    padding: 6px 16px;
    min-width: 100px;
  }
}
</style>
