<template>
  <van-form @submit="onSubmit">
    <!-- 性别单选 -->
    <template v-if="editUser.editKey === 'gender'">
      <van-field label="性别">
        <template #input>
          <van-radio-group v-model="editUser.currentValue" direction="horizontal">
            <van-radio name="1">男</van-radio>
            <van-radio name="0">女</van-radio>
          </van-radio-group>
        </template>
      </van-field>
    </template>

    <!-- 密码输入框 -->
    <template v-else-if="editUser.editKey === 'userPassword' || editUser.editKey === 'password'">
      <van-field
          v-model="editUser.currentValue"
          :name="editUser.editKey"
          :label="editUser.editName"
          type="password"
          placeholder="请输入新的密码"
          autocomplete="new-password"
      />
    </template>

    <!-- 其他文本输入 -->
    <template v-else>
      <van-field
          v-model="editUser.currentValue"
          :name="editUser.editKey"
          :label="editUser.editName"
          :placeholder="`请输入${editUser.editName}`"
      />
    </template>

    <div style="margin: 16px;">
      <van-button round block type="primary" native-type="submit">
        提交
      </van-button>
    </div>
  </van-form>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { ref } from "vue";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import { getCurrentUser } from "../services/user";

const route = useRoute();
const router = useRouter();

const editUser = ref({
  editKey: route.query.editKey as string,
  currentValue: route.query.currentValue as string,
  editName: route.query.editName as string,
});

const onSubmit = async () => {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    Toast.fail("用户未登录");
    return;
  }

  let valueToSend: any = editUser.value.currentValue;

  // 性别转数字
  if (editUser.value.editKey === "gender") {
    valueToSend = Number(valueToSend);
  }

  // 密码非空校验
  if (
      (editUser.value.editKey === "userPassword" || editUser.value.editKey === "password") &&
      (!valueToSend || valueToSend.trim() === "")
  ) {
    Toast.fail("请输入新的密码");
    return;
  }

  const res = await myAxios.post("/user/update", {
    id: currentUser.id,
    [editUser.value.editKey]: valueToSend,
  });

  if (res.code === 0 && res.data > 0) {
    Toast.success("修改成功");
    router.back();
  } else {
    Toast.fail("修改错误");
  }
};
</script>

<style scoped></style>
