<template>
  <div id="teamPage">
    <van-search v-model="searchText" placeholder="搜索队伍" @search="onSearch" />
    <van-tabs v-model:active="active" @change="onTabChange">
      <van-tab title="公开" name="public" />
      <van-tab title="加密" name="private" />
      <van-tab title="我创建的" name="my" />
    </van-tabs>
    <div style="margin-bottom: 16px" />

    <!-- 队伍列表 -->
    <div class="team-content">
      <team-card-list-1 :teamList="teamList" />
      <van-empty v-if="teamList?.length < 1 && !loading" description="暂无队伍数据"/>

      <!-- 加载状态 -->
      <van-loading v-if="loading" class="loading-container" size="24px" vertical>
        加载中...
      </van-loading>

      <!-- 分页组件 -->
      <div v-if="total > 0" class="pagination-container">
        <van-pagination
          v-model="currentPage"
          :total-items="total"
          :items-per-page="pageSize"
          :show-page-size="3"
          @change="onPageChange"
        />
        <div class="pagination-info">
          共 {{ total }} 个队伍，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
      </div>

      <!-- 更多提示 -->
      <div v-if="teamList?.length > 0 && currentPage >= totalPages" class="more-tip">
        已显示全部队伍
      </div>
    </div>

    <!-- 悬浮添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="toAddTeam"
      class="floating-add-btn"
    />
  </div>
</template>

<script setup lang="ts">

import {useRouter} from "vue-router";
import TeamCardList1 from "../components/TeamCardList-1.vue";
import {onMounted, ref} from "vue";
import myAxios from "../plugins/myAxios";
import {Toast} from "vant";

const active = ref('public')
const router = useRouter();
const searchText = ref('');

/**
 * 切换查询状态
 * @param name
 */
const onTabChange = (name) => {
  // 查公开
  if (name === 'public') {
    listTeam(searchText.value, 0);
  } else {
    // 查加密
    listTeam(searchText.value, 2);
  }
}

// 跳转到创建队伍页
const toAddTeam = () => {
  router.push({
    path: "/team/add"
  })
}

const teamList = ref([]);

/**
 * 搜索队伍
 * @param val
 * @param status
 * @returns {Promise<void>}
 */
const listTeam = async (val = '', status = 0) => {
  const res = await myAxios.get("/team/list", {
    params: {
      searchText: val,
      pageNum: 1,
      status,
    },
  });
  if (res?.code === 0) {
    teamList.value = res.data;
  } else {
    Toast.fail('加载队伍失败，请刷新重试');
  }
}

// 页面加载时只触发一次
onMounted( () => {
  listTeam();
})

const onSearch = (val) => {
  listTeam(val);
};

</script>

<style scoped>
#teamPage {

}
</style>
