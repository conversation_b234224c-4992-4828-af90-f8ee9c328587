<template>
  <div id="teamPage">
    <van-search v-model="searchText" placeholder="搜索队伍" @search="onSearch" />
    <van-tabs v-model:active="active" @change="onTabChange">
      <van-tab title="公开" name="public" />
      <van-tab title="加密" name="encrypted" />
      <van-tab title="我创建的" name="my" />
    </van-tabs>

    <!-- 队伍列表 -->
    <div class="team-content">
      <team-card-list-1 v-if="teamList?.length > 0" :teamList="teamList" />

      <!-- 加载状态 -->
      <van-loading v-if="loading" class="loading-container" size="24px" vertical>
        加载中...
      </van-loading>

      <!-- 空状态 -->
      <div v-if="teamList?.length < 1 && !loading" class="empty-state">
        <van-empty
          :image="getEmptyImage()"
          :description="getEmptyDescription()"
          class="custom-empty"
        >
          <template #description>
            <div class="empty-description">
              <h3>{{ getEmptyTitle() }}</h3>
              <p>{{ getEmptyDescription() }}</p>
              <van-button
                v-if="active !== 'my'"
                type="primary"
                round
                size="small"
                @click="toAddTeam"
                class="empty-action-btn"
              >
                <van-icon name="plus" />
                创建队伍
              </van-button>
              <van-button
                v-else
                type="primary"
                round
                size="small"
                @click="toAddTeam"
                class="empty-action-btn"
              >
                <van-icon name="plus" />
                创建第一个队伍
              </van-button>
            </div>
          </template>
        </van-empty>
      </div>

      <!-- 更多提示 -->
      <div v-if="teamList?.length > 0 && currentPage >= totalPages" class="more-tip">
        已显示全部队伍
      </div>

      <!-- 分页组件 -->
      <div v-if="total > 0" class="pagination-container">
        <van-pagination
          v-model="currentPage"
          :total-items="total"
          :items-per-page="pageSize"
          :show-page-size="3"
          @change="onPageChange"
        />
        <div class="pagination-info">
          共 {{ total }} 个队伍，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
      </div>
    </div>

    <!-- 悬浮添加按钮 -->
    <van-floating-bubble
      axis="xy"
      @click="toAddTeam"
      class="floating-add-btn"
    >
      <van-icon name="plus" class="floating-icon" />
    </van-floating-bubble>
  </div>
</template>

<script setup lang="ts">

import {useRouter} from "vue-router";
import TeamCardList1 from "../components/TeamCardList-1.vue";
import {onMounted, ref, computed} from "vue";
import myAxios from "../plugins/myAxios";
import {Toast} from "vant";
import {getCurrentUser} from "../services/user";

const active = ref('public')
const router = useRouter();
const searchText = ref('');
const loading = ref(false);
const currentUser = ref();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(6);
const total = ref(0);
const totalPages = computed(() => Math.ceil(total.value / pageSize.value));

/**
 * 切换查询状态
 * @param name
 */
const onTabChange = (name) => {
  currentPage.value = 1; // 切换标签时重置页码
  if (name === 'public') {
    listTeam(searchText.value, 0); // 公开队伍
  } else if (name === 'encrypted') {
    listTeam(searchText.value, 1); // 加密队伍
  } else if (name === 'my') {
    listMyTeam(searchText.value); // 我创建的队伍
  }
}

// 跳转到创建队伍页
const toAddTeam = () => {
  console.log('点击添加队伍按钮');
  router.push({
    path: "/team/add"
  })
}

const teamList = ref([]);

/**
 * 搜索队伍（分页版本）
 * @param val 搜索关键词
 * @param status 队伍状态
 * @returns {Promise<void>}
 */
const listTeam = async (val = '', status = 0) => {
  loading.value = true;
  try {
    const res = await myAxios.get("/team/list/page", {
      params: {
        searchText: val,
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        status,
      },
    });
    if (res?.code === 0 && res.data) {
      teamList.value = res.data.records || [];
      total.value = res.data.total || 0;
    } else {
      Toast.fail('加载队伍失败，请刷新重试');
      teamList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('加载队伍失败:', error);
    Toast.fail('加载队伍失败，请检查网络连接');
    teamList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

/**
 * 搜索我创建的队伍
 * @param val 搜索关键词
 * @returns {Promise<void>}
 */
const listMyTeam = async (val = '') => {
  if (!currentUser.value?.id) {
    Toast.fail('请先登录');
    return;
  }

  loading.value = true;
  try {
    const res = await myAxios.get("/team/list/page", {
      params: {
        searchText: val,
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        userId: currentUser.value.id, // 添加用户ID筛选我创建的队伍
      },
    });
    if (res?.code === 0 && res.data) {
      teamList.value = res.data.records || [];
      total.value = res.data.total || 0;
    } else {
      Toast.fail('加载队伍失败，请刷新重试');
      teamList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('加载我的队伍失败:', error);
    Toast.fail('加载队伍失败，请检查网络连接');
    teamList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

/**
 * 分页改变事件
 * @param page 页码
 */
const onPageChange = (page) => {
  currentPage.value = page;
  // 根据当前标签重新加载数据
  if (active.value === 'public') {
    listTeam(searchText.value, 0); // 公开队伍
  } else if (active.value === 'encrypted') {
    listTeam(searchText.value, 1); // 加密队伍
  } else if (active.value === 'my') {
    listMyTeam(searchText.value); // 我创建的队伍
  }
}

// 页面加载时只触发一次
onMounted(async () => {
  currentUser.value = await getCurrentUser();
  listTeam();
})

const onSearch = (val) => {
  currentPage.value = 1; // 搜索时重置页码
  if (active.value === 'public') {
    listTeam(val, 0); // 公开队伍
  } else if (active.value === 'encrypted') {
    listTeam(val, 1); // 加密队伍
  } else if (active.value === 'my') {
    listMyTeam(val); // 我创建的队伍
  }
};

// 获取空状态图片
const getEmptyImage = () => {
  if (active.value === 'public') {
    return 'search'; // 搜索图标
  } else if (active.value === 'encrypted') {
    return 'network'; // 网络图标
  } else if (active.value === 'my') {
    return 'default'; // 默认图标
  }
  return 'default';
};

// 获取空状态标题
const getEmptyTitle = () => {
  if (searchText.value) {
    return '未找到相关队伍';
  }

  if (active.value === 'public') {
    return '暂无公开队伍';
  } else if (active.value === 'encrypted') {
    return '暂无加密队伍';
  } else if (active.value === 'my') {
    return '您还没有创建队伍';
  }
  return '暂无队伍';
};

// 获取空状态描述
const getEmptyDescription = () => {
  if (searchText.value) {
    return `没有找到包含"${searchText.value}"的队伍，试试其他关键词吧`;
  }

  if (active.value === 'public') {
    return '目前还没有公开的队伍，快来创建第一个吧！';
  } else if (active.value === 'encrypted') {
    return '目前还没有加密的队伍，创建一个加密队伍吧！';
  } else if (active.value === 'my') {
    return '创建您的第一个队伍，开始组建团队吧！';
  }
  return '暂时没有队伍数据';
};

</script>

<style scoped>
#teamPage {
  position: relative;
  padding-bottom: 80px; /* 为悬浮按钮留出空间 */
}

.team-content {
  min-height: auto;
}

.loading-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分页容器 */
.pagination-container {
  margin: 24px 0;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.pagination-info {
  text-align: center;
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  padding: 20px;
  padding-top: 20px;
}

.custom-empty {
  background: transparent;
}

.empty-description {
  text-align: center;
  padding: 10px 0;
}

.empty-description h3 {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.empty-description p {
  font-size: 14px;
  color: #969799;
  margin: 0 0 24px 0;
  line-height: 1.6;
  max-width: 280px;
  margin-left: auto;
  margin-right: auto;
}

.empty-action-btn {
  background: #1989fa;
  border-color: #1989fa;
  font-size: 14px;
  padding: 8px 20px;
  min-width: 120px;
}

.empty-action-btn .van-icon {
  margin-right: 4px;
  font-size: 16px;
}

/* 更多提示 */
.more-tip {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

/* 悬浮添加按钮 */
.floating-add-btn {
  position: fixed !important;
  bottom: 80px;
  right: 20px;
  z-index: 9999 !important;
  background: #1989fa !important; /* Vant primary 颜色 */
  box-shadow: 0 4px 16px rgba(25, 137, 250, 0.4) !important;
  width: 56px !important;
  height: 56px !important;
  border-radius: 50% !important; /* 圆形 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
}

.floating-add-btn:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(25, 137, 250, 0.5) !important;
}

.floating-add-btn:active {
  transform: scale(0.95) !important;
}

.floating-icon {
  color: white !important;
  font-size: 20px !important;
  font-weight: 400 !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 自定义悬浮按钮样式 */
:deep(.van-floating-bubble) {
  background: #1989fa !important; /* Vant primary 颜色 */
  box-shadow: 0 4px 16px rgba(25, 137, 250, 0.4) !important;
  border: none !important;
  z-index: 9999 !important;
  width: 56px !important;
  height: 56px !important;
  border-radius: 50% !important; /* 圆形 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.van-floating-bubble .van-icon) {
  color: white !important;
  font-size: 20px !important;
  font-weight: 400 !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 分页组件样式优化 */
:deep(.van-pagination) {
  justify-content: center;
}

:deep(.van-pagination__item) {
  border-radius: 8px;
  margin: 0 4px;
}

:deep(.van-pagination__item--active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

/* 标签页样式优化 */
:deep(.van-tabs__line) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.van-tab--active) {
  color: #667eea;
  font-weight: 600;
}

/* 搜索框样式优化 */
:deep(.van-search) {
  background: #f8f9fa;
  padding: 12px 16px;
}

:deep(.van-search__content) {
  border-radius: 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-add-btn {
    bottom: 70px;
    right: 16px;
  }

  .pagination-container {
    margin: 16px 0;
    padding: 12px;
  }

  .pagination-info {
    font-size: 12px;
  }

  .empty-state {
    padding: 16px;
    padding-top: 16px;
  }

  .empty-description h3 {
    font-size: 16px;
  }

  .empty-description p {
    font-size: 13px;
    max-width: 240px;
  }

  .empty-action-btn {
    font-size: 13px;
    padding: 6px 16px;
    min-width: 100px;
  }
}
</style>
