<<template>
  <div style="padding: 12px">
    <user-card-list :user-list="userList" :loading="loading" />

    <van-empty
        v-if="!loading && userList.length < 1"
        description="搜索结果为空"
    />

    <van-pagination
        v-if="total > 0"
        v-model:current-page="pageNum"
        :total-items="total"
        :items-per-page="pageSize"
        mode="multi"
        @change="onPageChange"
        style="margin-top: 24px"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import myAxios from '../plugins/myAxios';
import { Toast } from 'vant';
import qs from 'qs';
import UserCardList from '../components/UserCardList.vue';
import type { UserType } from '../models/user';

const route = useRoute();
const { tags } = route.query;

const userList = ref<UserType[]>([]);
const loading = ref(false);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);

interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

const fetchUsers = async () => {
  loading.value = true;
  try {
    const response = await myAxios.get('/user/search/tags', {
      params: {
        tagNameList: tags,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
      },
      paramsSerializer: (params) => qs.stringify(params, { indices: false }),
    });

    const pageData: PageResponse<UserType> = response?.data;

    if (pageData?.records) {
      pageData.records.forEach((user) => {
        if (typeof user.tags === 'string') {
          try {
            user.tags = JSON.parse(user.tags);
          } catch {
            user.tags = [];
          }
        }
      });

      userList.value = pageData.records;
      total.value = pageData.total;
    }
  } catch (error) {
    console.error('/user/search/tags error', error);
    Toast.fail('请求失败');
  } finally {
    loading.value = false;
  }
};

// 关键点，接收参数，更新页码，触发请求
const onPageChange = (newPage: number) => {
  pageNum.value = newPage;
  fetchUsers();
};

onMounted(() => {
  fetchUsers();
});

watch(() => route.query.tags, () => {
  pageNum.value = 1;
  fetchUsers();
});
</script>
