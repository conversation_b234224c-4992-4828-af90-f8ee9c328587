<template>
  <van-form @submit="onSubmit">
    <van-field
        v-model="oldPassword"
        name="oldPassword"
        label="旧密码"
        type="password"
        placeholder="请输入旧密码"
        autocomplete="current-password"
        required
    />
    <van-field
        v-model="newPassword"
        name="newPassword"
        label="新密码"
        type="password"
        placeholder="请输入新密码"
        autocomplete="new-password"
        required
    />
    <van-field
        v-model="confirmPassword"
        name="confirmPassword"
        label="确认新密码"
        type="password"
        placeholder="请再次输入新密码"
        autocomplete="new-password"
        required
    />

    <div style="margin: 16px;">
      <van-button
          icon="setting-o"
          round block type="primary" native-type="submit">
        提交修改
      </van-button>
    </div>
  </van-form>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";
import { getCurrentUser } from "../services/user";

const router = useRouter();

const oldPassword = ref("");
const newPassword = ref("");
const confirmPassword = ref("");

const onSubmit = async () => {
  if (!oldPassword.value.trim()) {
    Toast.fail("请输入旧密码");
    return;
  }
  if (!newPassword.value.trim()) {
    Toast.fail("请输入新密码");
    return;
  }
  if (newPassword.value !== confirmPassword.value) {
    Toast.fail("两次输入的新密码不一致");
    return;
  }

  const currentUser = await getCurrentUser();
  if (!currentUser) {
    Toast.fail("用户未登录");
    return;
  }

  try {
    const res = await myAxios.post("/user/updatePassword", {
      id: currentUser.id,
      oldPassword: oldPassword.value,
      newPassword: newPassword.value,
    });

    if (res.code === 0) {
      Toast.success("密码修改成功");
      router.back();
    } else {
      Toast.fail(res.message || "密码修改失败");
    }
  } catch (error) {
    Toast.fail("请求失败，请稍后重试");
  }
};
</script>