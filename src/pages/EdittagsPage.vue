<template>
  <div class="edit-container">
    <van-field
        v-model="tagInput"
        label="标签"
        placeholder="请输入标签，用逗号分隔"
        type="textarea"
        rows="3"
        maxlength="200"
        show-word-limit
    />

    <div class="tips">
      <p>提示：</p>
      <p>• 多个标签用逗号分隔</p>
      <p>• 例如：学生,喜欢运动,编程爱好者</p>
    </div>

    <!-- 预览当前标签 -->
    <div class="preview" v-if="previewTags.length > 0">
      <p class="preview-title">预览：</p>
      <div class="tag-list">
        <van-tag
            v-for="(tag, index) in previewTags"
            :key="index"
            type="primary"
            size="medium"
            class="tag-item"
        >
          {{ tag }}
        </van-tag>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="save-section">
      <van-button
          type="primary"
          size="large"
          block
          @click="onSave"
          :loading="saving"
      >
        {{ saving ? '保存中...' : '保存' }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getCurrentUser } from "../services/user";
import myAxios from "../plugins/myAxios";
import { Toast } from "vant";

const router = useRouter();
const route = useRoute();
const tagInput = ref("");
const saving = ref(false);

onMounted(() => {
  // 从路由参数获取当前标签
  const currentTags = route.query.currentTags as string;
  if (currentTags && currentTags !== "[]") {
    try {
      const tagArray = JSON.parse(currentTags);
      if (Array.isArray(tagArray)) {
        tagInput.value = tagArray.join(",");
      }
    } catch (error) {
      console.error("解析标签失败:", error);
    }
  }
});

// 实时预览标签
const previewTags = computed(() => {
  if (!tagInput.value.trim()) return [];
  return tagInput.value
      .split(",")
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
});

const onSave = async () => {
  if (saving.value) return;

  try {
    saving.value = true;

    // 将输入的标签转换为 JSON 格式的字符串
    const tags = previewTags.value;
    const tagsJsonString = JSON.stringify(tags);

    // 获取当前用户信息
    const currentUser = await getCurrentUser();

    // 更新用户标签
    const res = await myAxios.post("/user/update", {
      id: currentUser.id,
      tags: tagsJsonString
    });

    if (res.code === 0 && res.data > 0) {
      Toast.success("标签保存成功");
      setTimeout(() => {
        router.back();
      }, 1000);
    } else {
      Toast.fail("标签保存失败");
    }
  } catch (error) {
    console.error("保存标签出错:", error);
    Toast.fail("保存出错");
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.edit-container {
  padding: 16px;
  min-height: calc(100vh - 96px);
  display: flex;
  flex-direction: column;
}

.tips {
  margin-top: 16px;
  padding: 12px;
  background-color: #f7f8fa;
  border-radius: 8px;
  font-size: 14px;
  color: #646566;
}

.tips p {
  margin: 4px 0;
}

.preview {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #ebedf0;
  border-radius: 8px;
}

.preview-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin-right: 0;
}

.save-section {
  margin-top: auto;
  padding-top: 24px;
  padding-bottom: 16px;
}
</style>